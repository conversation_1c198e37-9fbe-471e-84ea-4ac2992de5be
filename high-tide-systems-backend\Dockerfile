FROM node:20.17.0-alpine3.20

# Instalar dependências do sistema
RUN apk add --no-cache \
    python3 \
    make \
    g++ \
    git

WORKDIR /usr/src/app

# Configurar npm para contornar problemas de SSL
ENV NPM_CONFIG_LEGACY_PEER_DEPS=true
ENV NODE_OPTIONS="--openssl-legacy-provider"

# Copiar arquivos de configuração
COPY package*.json ./
COPY prisma ./prisma/

# Instalar dependências
RUN npm cache clean --force \
    && npm config set fetch-retry-maxtimeout 600000 \
    && npm config set fetch-retry-mintimeout 10000 \
    && npm config set fetch-retries 5 \
    && npm config set registry https://registry.npmjs.org/ \
    && npm install --no-fund --no-audit \
    && npx prisma generate \
    && npm install -g nodemon

# Copiar código da aplicação
COPY . .

EXPOSE 5000