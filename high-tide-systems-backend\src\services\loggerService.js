// src/services/loggerService.js
const winston = require('winston');
const path = require('path');

// Configuração de níveis de log customizados
const customLevels = {
  levels: {
    error: 0,
    warn: 1,
    info: 2,
    http: 3,
    debug: 4,
  },
  colors: {
    error: 'red',
    warn: 'yellow',
    info: 'green',
    http: 'magenta',
    debug: 'blue',
  }
};

// Adicionar cores aos níveis
winston.addColors(customLevels.colors);

// Formato personalizado para logs estruturados
const logFormat = winston.format.combine(
  winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss' }),
  winston.format.errors({ stack: true }),
  winston.format.json(),
  winston.format.printf(({ timestamp, level, message, ...meta }) => {
    const logObject = {
      timestamp,
      level,
      message,
      ...meta
    };
    
    // Adicionar informações de contexto se disponíveis
    if (meta.requestId) logObject.requestId = meta.requestId;
    if (meta.userId) logObject.userId = meta.userId;
    if (meta.companyId) logObject.companyId = meta.companyId;
    if (meta.ip) logObject.ip = meta.ip;
    if (meta.userAgent) logObject.userAgent = meta.userAgent;
    
    return JSON.stringify(logObject);
  })
);

// Formato para console (desenvolvimento)
const consoleFormat = winston.format.combine(
  winston.format.colorize({ all: true }),
  winston.format.timestamp({ format: 'HH:mm:ss' }),
  winston.format.printf(({ timestamp, level, message, ...meta }) => {
    let logMessage = `${timestamp} [${level}]: ${message}`;
    
    // Adicionar contexto importante no console
    if (meta.requestId) logMessage += ` [req:${meta.requestId}]`;
    if (meta.userId) logMessage += ` [user:${meta.userId}]`;
    if (meta.duration) logMessage += ` [${meta.duration}ms]`;
    
    // Adicionar stack trace para erros
    if (meta.stack) logMessage += `\n${meta.stack}`;
    
    return logMessage;
  })
);

// Configuração de transports
const transports = [];

// Console transport (sempre ativo em desenvolvimento)
if (process.env.NODE_ENV !== 'production') {
  transports.push(
    new winston.transports.Console({
      format: consoleFormat,
      level: process.env.LOG_LEVEL || 'debug'
    })
  );
}

// File transports (produção)
if (process.env.NODE_ENV === 'production') {
  // Log geral
  transports.push(
    new winston.transports.File({
      filename: path.join(process.cwd(), 'logs', 'app.log'),
      format: logFormat,
      level: 'info',
      maxsize: 10 * 1024 * 1024, // 10MB
      maxFiles: 5,
      tailable: true
    })
  );

  // Log de erros
  transports.push(
    new winston.transports.File({
      filename: path.join(process.cwd(), 'logs', 'error.log'),
      format: logFormat,
      level: 'error',
      maxsize: 10 * 1024 * 1024, // 10MB
      maxFiles: 5,
      tailable: true
    })
  );

  // Log de auditoria
  transports.push(
    new winston.transports.File({
      filename: path.join(process.cwd(), 'logs', 'audit.log'),
      format: logFormat,
      level: 'info',
      maxsize: 50 * 1024 * 1024, // 50MB
      maxFiles: 10,
      tailable: true
    })
  );
}

// Criar logger principal
const logger = winston.createLogger({
  levels: customLevels.levels,
  format: logFormat,
  transports,
  exitOnError: false,
  // Capturar exceções não tratadas
  exceptionHandlers: [
    new winston.transports.File({
      filename: path.join(process.cwd(), 'logs', 'exceptions.log'),
      format: logFormat
    })
  ],
  // Capturar rejeições de Promise não tratadas
  rejectionHandlers: [
    new winston.transports.File({
      filename: path.join(process.cwd(), 'logs', 'rejections.log'),
      format: logFormat
    })
  ]
});

// Logger especializado para auditoria
const auditLogger = winston.createLogger({
  levels: customLevels.levels,
  format: logFormat,
  transports: [
    new winston.transports.File({
      filename: path.join(process.cwd(), 'logs', 'audit.log'),
      format: logFormat,
      maxsize: 50 * 1024 * 1024, // 50MB
      maxFiles: 10,
      tailable: true
    })
  ]
});

// Logger especializado para performance
const performanceLogger = winston.createLogger({
  levels: customLevels.levels,
  format: logFormat,
  transports: [
    new winston.transports.File({
      filename: path.join(process.cwd(), 'logs', 'performance.log'),
      format: logFormat,
      maxsize: 20 * 1024 * 1024, // 20MB
      maxFiles: 5,
      tailable: true
    })
  ]
});

// Classe principal do serviço de logging
class LoggerService {
  constructor() {
    this.logger = logger;
    this.auditLogger = auditLogger;
    this.performanceLogger = performanceLogger;
  }

  // Métodos de logging básicos
  error(message, meta = {}) {
    this.logger.error(message, meta);
  }

  warn(message, meta = {}) {
    this.logger.warn(message, meta);
  }

  info(message, meta = {}) {
    this.logger.info(message, meta);
  }

  http(message, meta = {}) {
    this.logger.http(message, meta);
  }

  debug(message, meta = {}) {
    this.logger.debug(message, meta);
  }

  // Logging de auditoria
  audit(action, entityType, entityId, details = {}, context = {}) {
    this.auditLogger.info('AUDIT_EVENT', {
      action,
      entityType,
      entityId,
      details,
      ...context
    });
  }

  // Logging de performance
  performance(operation, duration, details = {}, context = {}) {
    this.performanceLogger.info('PERFORMANCE_METRIC', {
      operation,
      duration,
      details,
      ...context
    });
  }

  // Logging de segurança
  security(event, severity, details = {}, context = {}) {
    const logMethod = severity === 'high' ? 'error' : 'warn';
    this.logger[logMethod]('SECURITY_EVENT', {
      event,
      severity,
      details,
      ...context
    });
  }

  // Logging de API requests
  apiRequest(method, url, statusCode, duration, context = {}) {
    this.logger.http('API_REQUEST', {
      method,
      url,
      statusCode,
      duration,
      ...context
    });
  }

  // Logging de database operations
  database(operation, table, duration, details = {}, context = {}) {
    this.logger.debug('DATABASE_OPERATION', {
      operation,
      table,
      duration,
      details,
      ...context
    });
  }

  // Logging de cache operations
  cache(operation, key, hit, duration, context = {}) {
    this.logger.debug('CACHE_OPERATION', {
      operation,
      key,
      hit,
      duration,
      ...context
    });
  }

  // Criar child logger com contexto
  child(context = {}) {
    return {
      error: (message, meta = {}) => this.error(message, { ...context, ...meta }),
      warn: (message, meta = {}) => this.warn(message, { ...context, ...meta }),
      info: (message, meta = {}) => this.info(message, { ...context, ...meta }),
      http: (message, meta = {}) => this.http(message, { ...context, ...meta }),
      debug: (message, meta = {}) => this.debug(message, { ...context, ...meta }),
      audit: (action, entityType, entityId, details = {}) =>
        this.audit(action, entityType, entityId, details, context),
      performance: (operation, duration, details = {}) =>
        this.performance(operation, duration, details, context),
      security: (event, severity, details = {}) =>
        this.security(event, severity, details, context),
      database: (operation, table, duration, details = {}) =>
        this.database(operation, table, duration, details, context),
      cache: (operation, key, hit, duration) =>
        this.cache(operation, key, hit, duration, context)
    };
  }
}

// Criar instância singleton
const loggerService = new LoggerService();

module.exports = loggerService;
