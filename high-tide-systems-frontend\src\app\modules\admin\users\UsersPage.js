"use client";

import React, { useState, useEffect, useMemo, useCallback } from "react";
import {
  Plus,
  Search,
  Filter,
  RefreshCw,
  Edit,
  Trash,
  Shield,
  Power,
  CheckCircle,
  XCircle,
  Lock,
  UserCog,
  Briefcase,
  User
} from "lucide-react";
import ExportMenu from "@/components/ui/ExportMenu";
import { api } from "@/utils/api";
import { useAuth } from "@/contexts/AuthContext";
import { usePermissions } from "@/hooks/usePermissions";
import { Protected } from "@/components/permissions/Protected";
import UserFormModal from "@/components/users/UserFormModal";
import ModulesModal from "@/components/users/ModulesModal";
import ConfirmationDialog from "@/components/ui/ConfirmationDialog";
import PermissionsModal from "@/components/permissions/PermissionsModal";
import RoleModal from "@/components/users/RoleModal";
import { userService } from "@/app/modules/admin/services/userService";
import { companyService } from "@/app/modules/admin/services/companyService";
import { subscriptionService } from "@/services/subscriptionService";
import { ModuleSelect, ModuleTable, MultiSelect } from "@/components/ui";
import ModuleHeader, { FilterButton } from "@/components/ui/ModuleHeader";

const UsersPage = () => {
  const { user: currentUser } = useAuth();
  const { can } = usePermissions();
  const [users, setUsers] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [totalUsers, setTotalUsers] = useState(0);
  const [totalPages, setTotalPages] = useState(1);
  const [currentPage, setCurrentPage] = useState(1);
  const [search, setSearch] = useState("");
  const [statusFilter, setStatusFilter] = useState("");
  const [moduleFilter, setModuleFilter] = useState("");
  const [companyFilter, setCompanyFilter] = useState("");
  const [usersFilter, setUsersFilter] = useState([]);
  const [userOptions, setUserOptions] = useState([]);
  const [isLoadingUserOptions, setIsLoadingUserOptions] = useState(false);
  const [companies, setCompanies] = useState([]);
  const [isLoadingCompanies, setIsLoadingCompanies] = useState(false);
  const [userFormOpen, setUserFormOpen] = useState(false);
  const [selectedUser, setSelectedUser] = useState(null);
  const [modulesModalOpen, setModulesModalOpen] = useState(false);
  const [confirmationDialogOpen, setConfirmationDialogOpen] = useState(false);
  const [permissionsModalOpen, setPermissionsModalOpen] = useState(false);
  const [roleModalOpen, setRoleModalOpen] = useState(false);
  const [actionToConfirm, setActionToConfirm] = useState(null);
  const [isExporting, setIsExporting] = useState(false);
  const [subscriptionData, setSubscriptionData] = useState(null);
  const [isLoadingSubscription, setIsLoadingSubscription] = useState(false);

  // Constantes
  const ITEMS_PER_PAGE = 10;
  const MODULE_LABELS = {
    ADMIN: "Administração",
    RH: "RH",
    FINANCIAL: "Financeiro",
    SCHEDULING: "Agendamento",
    BASIC: "Básico",
  };

  // Verificar se o usuário atual é um system_admin ou company_admin
  const isSystemAdmin = currentUser?.role === "SYSTEM_ADMIN";
  const isAdmin = currentUser?.role === "SYSTEM_ADMIN" || currentUser?.role === "COMPANY_ADMIN";

  // Verificar se pode adicionar usuários baseado no limite da subscription
  const canAddUsers = subscriptionData?.usage?.canAddUsers !== false;

  // Função para carregar empresas (apenas para system_admin)
  const loadCompanies = async () => {
    if (!isSystemAdmin) return;

    setIsLoadingCompanies(true);
    try {
      const response = await companyService.getCompaniesForSelect();
      setCompanies(response);
    } catch (error) {
      console.error("Erro ao carregar empresas:", error);
    } finally {
      setIsLoadingCompanies(false);
    }
  };

  // Função para carregar dados da subscription
  const loadSubscriptionData = async () => {
    if (isSystemAdmin) return; // System admin não tem limite de usuários

    setIsLoadingSubscription(true);
    try {
      const response = await subscriptionService.getSubscription();
      setSubscriptionData(response);
    } catch (error) {
      console.error("Erro ao carregar dados da subscription:", error);
      // Se não conseguir carregar, assume que pode adicionar usuários
      setSubscriptionData({ usage: { canAddUsers: true } });
    } finally {
      setIsLoadingSubscription(false);
    }
  };

  // Função para carregar opções de usuários para o multi-select
  const loadUserOptions = useCallback(async () => {
    setIsLoadingUserOptions(true);
    try {
      // ✅ CORREÇÃO: Aplicar mesmo filtro de empresa para as opções
      const filters = {
        active: true, // Apenas usuários ativos por padrão
        // Company admin só vê usuários da sua empresa
        companyId: isSystemAdmin 
          ? undefined // System admin pode ver usuários de todas as empresas
          : (currentUser?.companyId || undefined), // Company admin só vê usuários da sua empresa
        excludeSystemAdmin: !isSystemAdmin
      };

      console.log('🔍 Filtros aplicados para loadUserOptions:', {
        userRole: currentUser?.role,
        isSystemAdmin,
        companyId: filters.companyId,
        filters
      });

      // Carregar todos os usuários para o multi-select (com limite maior)
      const response = await userService.list(1, 100, filters);

      console.log('📊 Opções de usuários carregadas:', {
        totalOptions: response.users?.length,
        firstOption: response.users?.[0]?.fullName
      });

      // Transformar os dados para o formato esperado pelo MultiSelect
      const options = response.users.map(user => ({
        value: user.id,
        label: user.fullName
      }));

      setUserOptions(options);
    } catch (error) {
      console.error("Erro ao carregar opções de usuários:", error);
    } finally {
      setIsLoadingUserOptions(false);
    }
  }, [isSystemAdmin, currentUser?.companyId]);

  const loadUsers = async (
    page = currentPage,
    searchQuery = search,
    status = statusFilter,
    module = moduleFilter,
    company = companyFilter,
    userIds = usersFilter,
    sortField = "fullName",
    sortDirection = "asc"
  ) => {
    setIsLoading(true);
    try {
      const filters = {
        search: searchQuery || undefined,
        active: status === "" ? undefined : status === "active",
        module: module || undefined,
        // Adiciona parâmetro para filtrar system_admin quando o usuário atual não for system_admin
        excludeSystemAdmin: !isSystemAdmin,
        // ✅ CORREÇÃO: Filtrar por empresa automaticamente para company_admin
        companyId: isSystemAdmin 
          ? (company || undefined) // System admin pode filtrar por qualquer empresa ou ver todas
          : (currentUser?.companyId || undefined), // Company admin só vê usuários da sua empresa
        // Adiciona parâmetro para filtrar por IDs específicos de usuários
        userIds: userIds.length > 0 ? userIds : undefined,
        // Adiciona parâmetros de ordenação
        sortField: sortField,
        sortDirection: sortDirection
      };

      console.log('🔍 Filtros aplicados para loadUsers:', {
        userRole: currentUser?.role,
        isSystemAdmin,
        companyId: filters.companyId,
        excludeSystemAdmin: filters.excludeSystemAdmin,
        allFilters: filters
      });

      const response = await userService.list(page, ITEMS_PER_PAGE, filters);

      console.log('📊 Resposta do userService.list:', {
        totalUsers: response.total,
        usersCount: response.users?.length,
        firstUser: response.users?.[0]?.fullName
      });

      setUsers(response.users);
      setTotalUsers(response.total);
      setTotalPages(response.pages);
      setCurrentPage(page);
    } catch (error) {
      console.error("Erro ao carregar usuários:", error);
      console.error("Detalhes do erro:", {
        message: error.message,
        response: error.response?.data,
        status: error.response?.status
      });
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    loadUsers();
    // Carregar empresas APENAS se o usuário for system_admin
    if (isSystemAdmin) {
      loadCompanies();
    }
    // Carregar dados da subscription para verificar limite de usuários (não se aplica a system_admin)
    loadSubscriptionData();
    // Carregar opções de usuários para o multi-select
    loadUserOptions();
  }, [loadUserOptions, isSystemAdmin]);

  const handleSearch = (e) => {
    e.preventDefault();
    loadUsers(1, search, statusFilter, moduleFilter, companyFilter, usersFilter);
  };

  const handleStatusFilterChange = (value) => {
    setStatusFilter(value);
    loadUsers(1, search, value, moduleFilter, companyFilter, usersFilter);
  };

  const handleModuleFilterChange = (value) => {
    setModuleFilter(value);
    loadUsers(1, search, statusFilter, value, companyFilter, usersFilter);
  };

  const handleCompanyFilterChange = (value) => {
    // ✅ CORREÇÃO: Company admin não pode alterar filtro de empresa
    if (!isSystemAdmin) {
      console.warn('Company admin tentou alterar filtro de empresa - bloqueado');
      return;
    }
    
    setCompanyFilter(value);
    loadUsers(1, search, statusFilter, moduleFilter, value, usersFilter);
  };

  const handleUsersFilterChange = (value) => {
    setUsersFilter(value);
    // ✅ CORREÇÃO: Manter lógica de empresa para company_admin
    const companyToFilter = isSystemAdmin ? companyFilter : currentUser?.companyId;
    loadUsers(1, search, statusFilter, moduleFilter, companyToFilter, value);
  };

  const handlePageChange = (page) => {
    loadUsers(page, search, statusFilter, moduleFilter, companyFilter, usersFilter);
  };

  const handleResetFilters = () => {
    setSearch("");
    setStatusFilter("");
    setModuleFilter("");
    // ✅ CORREÇÃO: Company admin não pode resetar filtro de empresa
    if (isSystemAdmin) {
      setCompanyFilter("");
    }
    setUsersFilter([]);
    
    // ✅ CORREÇÃO: Manter lógica de empresa para company_admin
    const companyToFilter = isSystemAdmin ? "" : currentUser?.companyId;
    loadUsers(1, "", "", "", companyToFilter, []);
  };

  const handleEditUser = (user) => {
    setSelectedUser(user);
    setUserFormOpen(true);
  };

  const handleEditModules = (user) => {
    setSelectedUser(user);
    setModulesModalOpen(true);
  };

  const handleManageRole = (user) => {
    setSelectedUser(user);
    setRoleModalOpen(true);
  };

  const handleToggleStatus = (user) => {
    setSelectedUser(user);
    setActionToConfirm({
      type: "toggle-status",
      message: `${user.active ? "Desativar" : "Ativar"} o usuário ${
        user.fullName
      }?`,
    });
    setConfirmationDialogOpen(true);
  };

  const handleDeleteUser = (user) => {
    setSelectedUser(user);
    setActionToConfirm({
      type: "delete",
      message: `Excluir permanentemente o usuário ${user.fullName}?`,
    });
    setConfirmationDialogOpen(true);
  };

  const handleManagePermissions = (user) => {
    setSelectedUser(user);
    setPermissionsModalOpen(true);
  };

  const handleExport = async (format) => {
    setIsExporting(true);
    try {
      // Exportar usando os mesmos filtros da tabela atual
      await userService.exportUsers({
        search: search || undefined,
        userIds: usersFilter.length > 0 ? usersFilter : undefined,
        active: statusFilter === "" ? undefined : statusFilter === "active",
        module: moduleFilter || undefined,
        companyId: companyFilter || undefined
      }, format);
    } catch (error) {
      console.error("Erro ao exportar usuários:", error);
    } finally {
      setIsExporting(false);
    }
  };

  const confirmAction = async () => {
    if (actionToConfirm.type === "toggle-status") {
      try {
        await userService.toggleStatus(selectedUser.id, !selectedUser.active);
        loadUsers();
        loadSubscriptionData(); // Recarregar dados da subscription
      } catch (error) {
        console.error("Erro ao alterar status do usuário:", error);
      }
    } else if (actionToConfirm.type === "delete") {
      try {
        await userService.delete(selectedUser.id);
        loadUsers();
        loadSubscriptionData(); // Recarregar dados da subscription
      } catch (error) {
        console.error("Erro ao excluir usuário:", error);
      }
    }
    setConfirmationDialogOpen(false);
  };

  // Import tutorial steps from tutorialMapping
  const admUsersTutorialSteps = useMemo(() => {
    // Import dynamically to avoid circular dependencies
    const tutorialMap = require('@/tutorials/tutorialMapping').default;
    return tutorialMap['/dashboard/admin/users'] || [];
  }, []);

  return (
    <div className="space-y-6">
      {/* Título e botões de exportar e adicionar */}
      <div className="flex justify-between items-center mb-4">
        <h1 className="text-2xl font-bold text-slate-800 dark:text-white flex items-center">
          <User size={24} className="mr-2 text-slate-600 dark:text-slate-400" />
          Usuários
        </h1>

        <div className="flex items-center gap-2">
          {/* Botão de exportar */}
          <ExportMenu
            onExport={handleExport}
            isExporting={isExporting}
            disabled={isLoading || users.length === 0}
            className="text-slate-700 dark:text-slate-300"
          />

          {/* Botão de adicionar */}
          {can("admin.users.create") && (
            <div className="relative group">
              <button
                onClick={() => {
                  if (canAddUsers) {
                    setSelectedUser(null);
                    setUserFormOpen(true);
                  }
                }}
                disabled={!canAddUsers}
                className={`flex items-center gap-2 px-4 py-2 rounded-lg shadow-md transition-all ${
                  canAddUsers
                    ? "bg-gradient-to-r from-slate-500 to-slate-600 dark:from-slate-600 dark:to-slate-700 text-white hover:from-slate-600 hover:to-slate-700 dark:hover:from-slate-700 dark:hover:to-slate-800"
                    : "bg-gray-300 dark:bg-gray-600 text-gray-500 dark:text-gray-400 cursor-not-allowed"
                }`}
              >
                <Plus size={18} />
                <span className="font-medium">Novo Usuário</span>
              </button>

              {/* Tooltip quando o botão está desabilitado */}
              {!canAddUsers && subscriptionData?.usage && (
                <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-3 py-2 bg-gray-800 text-white text-sm rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200 whitespace-nowrap z-10">
                  Limite de usuários atingido ({subscriptionData.usage.currentUsers}/{subscriptionData.usage.userLimit})
                  <div className="absolute top-full left-1/2 transform -translate-x-1/2 border-4 border-transparent border-t-gray-800"></div>
                </div>
              )}
            </div>
          )}
        </div>
      </div>

      {/* Cabeçalho e filtros da página */}
      <ModuleHeader
        title="Filtros"
        icon={<Filter size={22} className="text-module-admin-icon dark:text-module-admin-icon-dark" />}
        description="Gerencie os usuários do sistema. Utilize os filtros abaixo para encontrar usuários específicos."
        moduleColor="admin"
        tutorialSteps={admUsersTutorialSteps}
        tutorialName="admin-users-overview"
        filters={
          <form
            onSubmit={handleSearch}
            className="flex flex-col gap-4"
            id="filtroUsuario"
          >
            <div className="flex flex-col md:flex-row gap-4">
              <div className="flex-1 relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 dark:text-gray-500 h-5 w-5" />
                <input
                  type="text"
                  placeholder="Buscar por nome, email ou login..."
                  value={search}
                  onChange={(e) => setSearch(e.target.value)}
                  className="w-full pl-10 pr-4 py-2 border border-neutral-200 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-300 dark:focus:ring-primary-600 bg-white dark:bg-gray-700 text-neutral-800 dark:text-neutral-100"
                />
              </div>

              <div className="flex flex-col sm:flex-row gap-2">
                <div className="w-full sm:w-40">
                  <ModuleSelect
                    moduleColor="admin"
                    value={statusFilter}
                    onChange={(e) => handleStatusFilterChange(e.target.value)}
                    placeholder="Status"
                  >
                    <option value="">Todos os status</option>
                    <option value="active">Ativos</option>
                    <option value="inactive">Inativos</option>
                  </ModuleSelect>
                </div>

                <div className="w-full sm:w-48">
                  <ModuleSelect
                    moduleColor="admin"
                    value={moduleFilter}
                    onChange={(e) => handleModuleFilterChange(e.target.value)}
                    placeholder="Módulos"
                  >
                    <option value="">Todos os módulos</option>
                    <option value="ADMIN">Administração</option>
                    <option value="RH">RH</option>
                    <option value="FINANCIAL">Financeiro</option>
                    <option value="SCHEDULING">Agendamento</option>
                    <option value="BASIC">Básico</option>
                  </ModuleSelect>
                </div>

                {/* Filtro de empresa (APENAS para system_admin) */}
                {isSystemAdmin && (
                  <div className="w-full sm:w-48">
                    <ModuleSelect
                      moduleColor="admin"
                      value={companyFilter}
                      onChange={(e) => handleCompanyFilterChange(e.target.value)}
                      placeholder="Empresa"
                      disabled={isLoadingCompanies}
                    >
                      <option value="">Todas as empresas</option>
                      {companies.map((company) => (
                        <option key={company.id} value={company.id}>
                          {company.name}
                        </option>
                      ))}
                    </ModuleSelect>
                  </div>
                )}

                <FilterButton type="submit" moduleColor="admin" variant="primary">
                  <Filter size={16} className="sm:hidden" />
                  <span className="hidden sm:inline">Filtrar</span>
                </FilterButton>

                <FilterButton
                  type="button"
                  onClick={handleResetFilters}
                  moduleColor="admin"
                  variant="secondary"
                >
                  <RefreshCw size={16} className="sm:hidden" />
                  <span className="hidden sm:inline">Limpar</span>
                </FilterButton>
              </div>
            </div>

            {/* Multi-select para filtrar por múltiplos usuários */}
            <div className="w-full">
              <MultiSelect
                label="Filtrar por Usuários"
                value={usersFilter}
                onChange={handleUsersFilterChange}
                options={userOptions}
                placeholder="Selecione um ou mais usuários pelo nome..."
                loading={isLoadingUserOptions}
                moduleOverride="admin"
              />
            </div>
          </form>
        }
      />

      {/* Tabela de Usuários */}
      <ModuleTable
        moduleColor="admin"
        columns={[
          { header: 'Usuário', field: 'fullName', width: '20%' },
          { header: 'Email', field: 'email', width: '15%' },
          { header: 'Profissão', field: 'profession', width: '15%' },
          { header: 'Módulos', field: 'modules', width: '10%' },
          { header: 'Função', field: 'role', width: '10%' },
          { header: 'Status', field: 'active', width: '8%' },
          { header: 'Ações', field: 'actions', className: 'text-right', width: '14%', sortable: false }
        ]}
        data={users}
        isLoading={isLoading}
        emptyMessage="Nenhum usuário encontrado"
        emptyIcon={<User size={24} />}
        currentPage={currentPage}
        totalPages={totalPages}
        totalItems={totalUsers}
        onPageChange={handlePageChange}
        showPagination={true}
        tableId="admin-users-table"
        enableColumnToggle={true}
        defaultSortField="fullName"
        defaultSortDirection="asc"
        onSort={(field, direction) => {
          // Quando a ordenação mudar, recarregar os usuários com os novos parâmetros de ordenação
          loadUsers(
            currentPage,
            search,
            statusFilter,
            moduleFilter,
            companyFilter,
            usersFilter,
            field,
            direction
          );
        }}
        renderRow={(user, index, moduleColors, visibleColumns) => (
          <tr key={user.id} className={moduleColors.hoverBg}>
            {visibleColumns.includes('fullName') && (
              <td className="px-4 py-4">
                <div className="flex items-center">
                  <div className="h-10 w-10 rounded-full bg-neutral-200 dark:bg-gray-600 flex items-center justify-center text-neutral-600 dark:text-neutral-300 font-medium flex-shrink-0 overflow-hidden">
                    {user.profileImageFullUrl ? (
                      <img
                        src={user.profileImageFullUrl}
                        alt={user.fullName}
                        className="w-full h-full object-cover"
                        onError={(e) => {
                          e.target.onerror = null;
                          e.target.style.display = 'none';
                          e.target.parentNode.innerHTML = user.fullName.charAt(0).toUpperCase();
                        }}
                      />
                    ) : (
                      user.fullName.charAt(0).toUpperCase()
                    )}
                  </div>
                  <div className="ml-3 min-w-0">
                    <p className="text-sm font-medium text-neutral-800 dark:text-neutral-100 truncate">
                      {user.fullName}
                    </p>
                    <p className="text-xs text-neutral-500 dark:text-neutral-400 truncate">
                      {user.login}
                    </p>
                  </div>
                </div>
              </td>
            )}

            {visibleColumns.includes('email') && (
              <td className="px-4 py-4 text-sm text-neutral-600 dark:text-neutral-300 truncate">
                {user.email}
              </td>
            )}

            {visibleColumns.includes('profession') && (
              <td className="px-4 py-4 text-sm text-neutral-600 dark:text-neutral-300">
                {user.professionObj ? (
                  <div className="flex items-center gap-1">
                    <Briefcase size={14} className="text-neutral-500 dark:text-neutral-400 flex-shrink-0" />
                    <span className="truncate">
                      {user.professionObj.name}
                      {user.professionObj.group && (
                        <span className="text-xs text-neutral-400 dark:text-neutral-500 ml-1">
                          ({user.professionObj.group.name})
                        </span>
                      )}
                    </span>
                  </div>
                ) : (
                  <span className="text-neutral-400 dark:text-neutral-500 italic">Sem profissão</span>
                )}
              </td>
            )}

            {visibleColumns.includes('modules') && (
              <td className="px-4 py-4">
                <div className="flex flex-wrap gap-1">
                  {user.modules.slice(0, 2).map((module) => (
                    <span
                      key={module}
                      className="px-2 py-1 text-xs rounded-full bg-neutral-100 dark:bg-gray-700 text-neutral-700 dark:text-neutral-300"
                    >
                      {MODULE_LABELS[module]}
                    </span>
                  ))}
                  {user.modules.length > 2 && (
                    <span className="px-2 py-1 text-xs rounded-full bg-neutral-100 dark:bg-gray-700 text-neutral-700 dark:text-neutral-300">
                      +{user.modules.length - 2}
                    </span>
                  )}
                </div>
              </td>
            )}

            {visibleColumns.includes('role') && (
              <td className="px-4 py-4">
                <span
                  className={`px-2 py-1 text-xs rounded-full inline-flex items-center gap-1 ${
                    user.role === "SYSTEM_ADMIN"
                      ? "bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-400"
                      : user.role === "COMPANY_ADMIN"
                      ? "bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-400"
                      : "bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-400"
                  }`}
                >
                  <UserCog size={12} className="flex-shrink-0" />
                  <span className="truncate">
                    {user.role === "SYSTEM_ADMIN"
                      ? "Admin Sistema"
                      : user.role === "COMPANY_ADMIN"
                      ? "Admin Empresa"
                      : "Funcionário"}
                  </span>
                </span>
              </td>
            )}

            {visibleColumns.includes('active') && (
              <td className="px-4 py-4">
                <span
                  className={`px-2 py-1 text-xs rounded-full flex items-center gap-1 w-fit ${
                    user.active
                      ? "bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-400"
                      : "bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-400"
                  }`}
                >
                  {user.active ? (
                    <>
                      <CheckCircle size={12} className="flex-shrink-0" />
                      <span>Ativo</span>
                    </>
                  ) : (
                    <>
                      <XCircle size={12} className="flex-shrink-0" />
                      <span>Inativo</span>
                    </>
                  )}
                </span>
              </td>
            )}

            {visibleColumns.includes('actions') && (
              <td className="px-4 py-4 text-right text-sm font-medium">
                <div className="flex justify-end gap-1">
                <Protected permission="admin.users.edit">
                  <button
                    onClick={() => handleEditUser(user)}
                    className="p-1 text-neutral-500 dark:text-neutral-400 hover:text-primary-500 dark:hover:text-primary-400 transition-colors"
                    id="edicaoUsuario"
                    title="Editar"
                  >
                    <Edit size={16} />
                  </button>
                </Protected>

                <Protected permission="admin.permissions.manage">
                  <button
                    onClick={() => handleEditModules(user)}
                    className="p-1 text-neutral-500 dark:text-neutral-400 hover:text-blue-500 dark:hover:text-blue-400 transition-colors"
                    id="gerenciarModulo"
                    title="Gerenciar módulos"
                  >
                    <Shield size={16} />
                  </button>
                </Protected>

                <Protected permission="admin.permissions.manage">
                  <button
                    onClick={() => handleManagePermissions(user)}
                    className="p-1 text-neutral-500 dark:text-neutral-400 hover:text-purple-500 dark:hover:text-purple-400 transition-colors"
                    id="gerenciarPermissoes"
                    title="Gerenciar permissões"
                  >
                    <Lock size={16} />
                  </button>
                </Protected>

                {/* Não permitir gerenciar funções de SYSTEM_ADMIN se o usuário não for SYSTEM_ADMIN */}
                {(user.role !== "SYSTEM_ADMIN" || isSystemAdmin) && (
                  <Protected permission="admin.users.edit">
                    <button
                      onClick={() => handleManageRole(user)}
                      className="p-1 text-neutral-500 dark:text-neutral-400 hover:text-indigo-500 dark:hover:text-indigo-400 transition-colors"
                      id="gerenciarFuncao"
                      title="Alterar função"
                    >
                      <UserCog size={16} />
                    </button>
                  </Protected>
                )}

                {/* Não permitir alterar status de SYSTEM_ADMIN se o usuário não for SYSTEM_ADMIN */}
                {(user.role !== "SYSTEM_ADMIN" || isSystemAdmin) && (
                  <Protected permission="admin.users.edit">
                    <button
                      onClick={() => handleToggleStatus(user)}
                      className={`p-1 transition-colors ${
                        user.active
                          ? "text-neutral-500 dark:text-neutral-400 hover:text-amber-500 dark:hover:text-amber-400"
                          : "text-neutral-500 dark:text-neutral-400 hover:text-green-500 dark:hover:text-green-400"
                      }`}
                      id="desativarUsuario"
                      title={user.active ? "Desativar" : "Ativar"}
                    >
                      <Power size={16} />
                    </button>
                  </Protected>
                )}

                {/* Não permitir excluir SYSTEM_ADMIN se o usuário não for SYSTEM_ADMIN */}
                {(user.role !== "SYSTEM_ADMIN" || isSystemAdmin) && (
                  <Protected permission="admin.users.delete">
                    <button
                      onClick={() => handleDeleteUser(user)}
                      className="p-1 text-neutral-500 dark:text-neutral-400 hover:text-red-500 dark:hover:text-red-400 transition-colors"
                      id="excluirUsuario"
                      title="Excluir"
                    >
                      <Trash size={16} />
                    </button>
                  </Protected>
                )}
              </div>
            </td>
            )}
          </tr>
        )}
      />

      {/* Modais */}
      <UserFormModal
        isOpen={userFormOpen}
        onClose={() => setUserFormOpen(false)}
        user={selectedUser}
        onSuccess={() => {
          setUserFormOpen(false);
          loadUsers();
          loadSubscriptionData(); // Recarregar dados da subscription
        }}
        currentUser={currentUser}
      />

      <ModulesModal
        isOpen={modulesModalOpen}
        onClose={() => setModulesModalOpen(false)}
        user={selectedUser}
        onSuccess={() => {
          setModulesModalOpen(false);
          loadUsers();
        }}
      />

      <ConfirmationDialog
        isOpen={confirmationDialogOpen}
        onClose={() => setConfirmationDialogOpen(false)}
        onConfirm={confirmAction}
        title="Confirmar ação"
        message={actionToConfirm?.message || ""}
      />

      <PermissionsModal
        isOpen={permissionsModalOpen}
        onClose={() => setPermissionsModalOpen(false)}
        user={selectedUser}
        onSuccess={() => {
          setPermissionsModalOpen(false);
          loadUsers();
        }}
      />
      <RoleModal
        isOpen={roleModalOpen}
        onClose={() => setRoleModalOpen(false)}
        user={selectedUser}
        onSuccess={() => {
          setRoleModalOpen(false);
          loadUsers();
        }}
      />
    </div>
  );
};

export default UsersPage;