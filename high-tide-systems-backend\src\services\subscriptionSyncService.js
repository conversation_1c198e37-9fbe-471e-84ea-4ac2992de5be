// src/services/subscriptionSyncService.js
const prisma = require('../utils/prisma');

/**
 * Serviço para sincronizar módulos de usuários com assinaturas da empresa
 */
class SubscriptionSyncService {
  /**
   * Sincroniza os módulos de um usuário com os módulos contratados pela empresa
   * @param {string} userId - ID do usuário
   * @param {string} companyId - ID da empresa
   * @returns {Promise<Array>} - Módulos sincronizados
   */
  async syncUserModulesWithSubscription(userId, companyId) {
    try {
      // Buscar usuário atual
      const user = await prisma.user.findUnique({
        where: { id: userId },
        select: { 
          id: true, 
          role: true, 
          modules: true,
          companyId: true
        }
      });

      if (!user) {
        throw new Error('Usuário não encontrado');
      }

      // System Admin sempre tem todos os módulos
      if (user.role === 'SYSTEM_ADMIN') {
        return user.modules;
      }

      // Buscar módulos contratados pela empresa
      const contractedModules = await this.getCompanyContractedModules(companyId);
      
      // Para Company Admin, dar acesso a todos os módulos contratados
      if (user.role === 'COMPANY_ADMIN') {
        await prisma.user.update({
          where: { id: userId },
          data: { modules: contractedModules }
        });
        return contractedModules;
      }

      // Para funcionários, manter apenas módulos que estão contratados
      const userModules = user.modules || [];
      const allowedModules = userModules.filter(module => 
        contractedModules.includes(module)
      );

      // Sempre garantir que BASIC esteja incluído se contratado
      if (contractedModules.includes('BASIC') && !allowedModules.includes('BASIC')) {
        allowedModules.push('BASIC');
      }

      // Atualizar módulos do usuário
      await prisma.user.update({
        where: { id: userId },
        data: { modules: allowedModules }
      });

      return allowedModules;
    } catch (error) {
      console.error('Erro ao sincronizar módulos do usuário:', error);
      throw error;
    }
  }

  /**
   * Obtém os módulos contratados por uma empresa
   * @param {string} companyId - ID da empresa
   * @returns {Promise<Array>} - Lista de módulos contratados
   */
  async getCompanyContractedModules(companyId) {
    try {
      const subscription = await prisma.subscription.findUnique({
        where: { companyId },
        include: {
          modules: {
            where: { active: true }
          }
        }
      });

      if (!subscription || !subscription.active) {
        return ['BASIC']; // Módulo básico sempre disponível
      }

      return subscription.modules.map(module => module.moduleType);
    } catch (error) {
      console.error('Erro ao buscar módulos contratados:', error);
      return ['BASIC'];
    }
  }

  /**
   * Sincroniza todos os usuários de uma empresa com os módulos contratados
   * @param {string} companyId - ID da empresa
   * @returns {Promise<Object>} - Resultado da sincronização
   */
  async syncAllCompanyUsers(companyId) {
    try {
      const users = await prisma.user.findMany({
        where: { 
          companyId,
          active: true,
          role: { not: 'SYSTEM_ADMIN' } // System Admin não precisa de sincronização
        },
        select: { id: true, role: true, fullName: true }
      });

      const results = {
        success: 0,
        errors: 0,
        details: []
      };

      for (const user of users) {
        try {
          const syncedModules = await this.syncUserModulesWithSubscription(user.id, companyId);
          results.success++;
          results.details.push({
            userId: user.id,
            userName: user.fullName,
            status: 'success',
            modules: syncedModules
          });
        } catch (error) {
          results.errors++;
          results.details.push({
            userId: user.id,
            userName: user.fullName,
            status: 'error',
            error: error.message
          });
        }
      }

      return results;
    } catch (error) {
      console.error('Erro ao sincronizar usuários da empresa:', error);
      throw error;
    }
  }

  /**
   * Verifica se um usuário tem acesso a um módulo específico
   * @param {string} userId - ID do usuário
   * @param {string} moduleType - Tipo do módulo
   * @returns {Promise<boolean>} - Se o usuário tem acesso
   */
  async userHasModuleAccess(userId, moduleType) {
    try {
      const user = await prisma.user.findUnique({
        where: { id: userId },
        select: { 
          role: true, 
          modules: true, 
          companyId: true 
        }
      });

      if (!user) {
        return false;
      }

      // System Admin sempre tem acesso
      if (user.role === 'SYSTEM_ADMIN') {
        return true;
      }

      // Verificar se o usuário tem o módulo
      if (!user.modules?.includes(moduleType)) {
        return false;
      }

      // Verificar se a empresa tem o módulo contratado
      const contractedModules = await this.getCompanyContractedModules(user.companyId);
      return contractedModules.includes(moduleType);
    } catch (error) {
      console.error('Erro ao verificar acesso do usuário ao módulo:', error);
      return false;
    }
  }
}

module.exports = new SubscriptionSyncService();
