// src/controllers/companyController.js
const { validationResult } = require("express-validator");
const { body } = require("express-validator");
const prisma = require("../utils/prisma");
const subscriptionSyncService = require("../services/subscriptionSyncService");

// Validações
const companyValidation = [
  body("name").notEmpty().withMessage("Nome é obrigatório"),
  body("cnpj")
    .optional()
    .custom((value, { req }) => {
      // Strip all non-digit characters
      const cleaned = value ? value.replace(/\D/g, "") : "";

      // Store cleaned value for the controller
      req.body.cleanedCnpj = cleaned;

      // Check if it's empty or has exactly 14 digits
      if (cleaned === "" || /^\d{14}$/.test(cleaned)) {
        return true;
      }
      throw new Error("CNPJ inválido");
    }),
  body("phone")
    .optional()
    // Make this more lenient or remove validation since frontend handles format
    .custom(() => true),
  body("socialMedia")
    .optional()
    .custom((value, { req }) => {
      try {
        // If it's a string, try to parse it
        if (typeof value === "string") {
          const parsed = JSON.parse(value);
          // Store the parsed object for the controller
          req.body.parsedSocialMedia = parsed;
          return true;
        }
        // If it's already an object, that's fine too
        if (typeof value === "object" && value !== null) {
          req.body.parsedSocialMedia = value;
          return true;
        }
        throw new Error("Redes sociais deve ser um objeto");
      } catch (error) {
        throw new Error("Redes sociais deve ser um objeto válido");
      }
    }),
  body("businessHours")
    .optional()
    .custom((value, { req }) => {
      try {
        // If it's a string, try to parse it
        if (typeof value === "string") {
          const parsed = JSON.parse(value);
          req.body.parsedBusinessHours = parsed;
          return true;
        }
        // If it's already an object, that's fine too
        if (typeof value === "object" && value !== null) {
          req.body.parsedBusinessHours = value;
          return true;
        }
        return true; // Allow null/undefined
      } catch (error) {
        throw new Error("Horário de funcionamento deve ser um objeto válido");
      }
    }),
];

class CompanyController {
  /**
   * Cria uma nova empresa no sistema
   */
  static async create(req, res) {
    try {
      // Verificar se o usuário é SYSTEM_ADMIN para poder criar empresas
      if (req.user.role !== "SYSTEM_ADMIN") {
        return res.status(403).json({
          message: "Apenas administradores de sistema podem criar empresas",
        });
      }

      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({ errors: errors.array() });
      }

      const {
        name,
        tradingName,
        cnpj,
        phone,
        phone2,
        address,
        city,
        state,
        postalCode,
        website,
        primaryColor,
        secondaryColor,
        description,
      } = req.body;

      // Use the pre-cleaned/parsed values from validation
      const cleanedCnpj =
        req.body.cleanedCnpj || (cnpj ? cnpj.replace(/\D/g, "") : null);
      const socialMedia = req.body.parsedSocialMedia || {};
      const businessHours = req.body.parsedBusinessHours || {};

      // Verify if CNPJ already exists
      if (cleanedCnpj) {
        const existingCompany = await prisma.company.findUnique({
          where: { cnpj: cleanedCnpj },
        });

        if (existingCompany) {
          return res.status(400).json({ message: "CNPJ já cadastrado" });
        }
      }

      // Create the company
      const company = await prisma.company.create({
        data: {
          name,
          tradingName,
          cnpj: cleanedCnpj,
          phone,
          phone2,
          address,
          city,
          state,
          postalCode,
          website,
          primaryColor,
          secondaryColor,
          description,
          socialMedia,
          businessHours,
        },
      });

      // Handle logo file upload if present
      if (req.file) {
        await prisma.document.create({
          data: {
            filename: req.file.originalname,
            path: req.file.path,
            type: "LOGO",
            mimeType: req.file.mimetype,
            size: req.file.size,
            ownerType: "COMPANY",
            companyId: company.id,
            createdById: req.user.id,
          },
        });
      }

      res.status(201).json(company);
    } catch (error) {
      console.error("Erro ao criar empresa:", error);
      res.status(500).json({ message: "Erro interno do servidor" });
    }
  }

  /**
   * Obtém uma empresa específica
   */
  static async get(req, res) {
    try {
      const { id } = req.params;
      console.log('[companyController] Buscando empresa com ID:', id);

      // Verificar se o usuário tem permissão para ver a empresa
      if (req.user.role !== "SYSTEM_ADMIN" && req.user.companyId !== id) {
        console.log('[companyController] Acesso negado: usuário não tem permissão');
        return res.status(403).json({
          message: "Você não tem permissão para acessar esta empresa",
        });
      }

      const company = await prisma.company.findUnique({
        where: { id },
        include: {
          emailConfigs: {
            where: { active: true },
            select: {
              id: true,
              smtpHost: true,
              smtpPort: true,
              smtpSecure: true,
              smtpUser: true,
              emailFromName: true,
              emailFromAddress: true,
              active: true,
              createdAt: true,
            },
          },
          documents: {
            where: { type: "LOGO" },
            select: {
              id: true,
              filename: true,
              path: true,
              createdAt: true,
            },
          },
          subscription: {
            include: {
              modules: {
                where: { active: true }
              },
              invoices: {
                orderBy: { createdAt: 'desc' },
                take: 10, // Últimas 10 faturas para detalhes
                select: {
                  id: true,
                  status: true,
                  dueDate: true,
                  paidAt: true,
                  amount: true,
                  stripeInvoiceUrl: true,
                  createdAt: true
                }
              }
            }
          },
          users: {
            where: { active: true },
            select: {
              id: true,
              fullName: true,
              role: true,
              createdAt: true
            },
          },
        },
      });

      console.log('[companyController] Resultado da consulta:', company ? 'Empresa encontrada' : 'Empresa não encontrada');

      // Verificar se a empresa tem logo
      if (company && company.documents && company.documents.length > 0) {
        console.log('[companyController] Logo encontrado:', company.documents[0]);
        console.log('[companyController] Caminho do logo:', company.documents[0].path);

        // Construir URL pública para o logo
        const filename = company.documents[0].path.split('/').pop();
        const publicUrl = `/uploads/${filename}`;
        console.log('[companyController] URL pública sugerida:', publicUrl);
      } else {
        console.log('[companyController] Empresa não tem logo');
      }

      if (!company) {
        return res.status(404).json({ message: "Empresa não encontrada" });
      }

      // Processar o caminho do logo para facilitar o acesso no frontend
      if (company.documents && company.documents.length > 0) {
        const document = company.documents[0];
        console.log('[companyController] Processando caminho do logo para resposta:', document.path);

        // Manter o caminho original completo para referência
        document.originalPath = document.path;

        // Se o caminho começa com /usr/src/app/uploads, manter o caminho completo
        // para que o frontend possa extrair a parte relativa corretamente
        if (document.path.startsWith('/usr/src/app/uploads/')) {
          console.log('[companyController] Mantendo caminho completo do Docker para processamento no frontend');
        } else {
          // Caso contrário, extrair apenas o nome do arquivo
          const filename = document.path.split('/').pop();
          console.log('[companyController] Nome do arquivo extraído:', filename);
          document.path = filename; // Simplificar para apenas o nome do arquivo
        }
      }

      // Processar informações detalhadas de assinatura
      if (company.subscription) {
        const subscription = company.subscription;
        const now = new Date();

        // Calcular status de pagamento detalhado
        let paymentStatus = 'up_to_date';
        let hasOverdueInvoices = false;
        let overdueAmount = 0;
        let nextInvoiceAmount = 0;

        if (subscription.invoices && subscription.invoices.length > 0) {
          const overdueInvoices = subscription.invoices.filter(invoice =>
            invoice.status === 'PENDING' && new Date(invoice.dueDate) < now
          );

          if (overdueInvoices.length > 0) {
            hasOverdueInvoices = true;
            paymentStatus = 'overdue';
            overdueAmount = overdueInvoices.reduce((sum, invoice) => sum + parseFloat(invoice.amount), 0);
          }

          // Próxima fatura pendente
          const nextInvoice = subscription.invoices.find(invoice =>
            invoice.status === 'PENDING' && new Date(invoice.dueDate) >= now
          );
          if (nextInvoice) {
            nextInvoiceAmount = parseFloat(nextInvoice.amount);
          }
        }

        // Calcular próximo vencimento e dias restantes
        let nextDueDate = null;
        let daysUntilExpiry = null;
        let isNearExpiry = false;

        if (subscription.nextBillingDate) {
          nextDueDate = subscription.nextBillingDate;
        } else if (subscription.stripeCurrentPeriodEnd) {
          nextDueDate = subscription.stripeCurrentPeriodEnd;
        }

        if (nextDueDate) {
          daysUntilExpiry = Math.ceil((new Date(nextDueDate) - now) / (1000 * 60 * 60 * 24));
          isNearExpiry = daysUntilExpiry <= 7 && daysUntilExpiry > 0;

          if (daysUntilExpiry < 0) {
            paymentStatus = 'expired';
          }
        }

        // Calcular estatísticas de uso
        const userCount = company.users ? company.users.length : 0;
        const userLimit = subscription.userLimit || 50;
        const userUsagePercentage = userLimit > 0 ? Math.round((userCount / userLimit) * 100) : 0;

        // Adicionar informações processadas detalhadas
        company.subscriptionInfo = {
          // Informações básicas do plano
          planName: company.plan || 'Plano Básico',
          status: subscription.status,
          billingCycle: subscription.billingCycle,
          pricePerMonth: subscription.pricePerMonth,

          // Status de pagamento
          paymentStatus,
          hasOverdueInvoices,
          overdueAmount,
          nextInvoiceAmount,

          // Datas e vencimentos
          nextDueDate,
          daysUntilExpiry,
          isNearExpiry,
          startDate: subscription.startDate,
          endDate: subscription.endDate,
          trialEndDate: subscription.trialEndDate,

          // Módulos
          moduleCount: subscription.modules ? subscription.modules.length : 0,
          modules: subscription.modules ? subscription.modules.map(m => ({
            type: m.moduleType,
            active: m.active,
            addedAt: m.addedAt,
            pricePerMonth: m.pricePerMonth
          })) : [],

          // Usuários
          userCount,
          userLimit,
          userUsagePercentage,
          isNearUserLimit: userUsagePercentage >= 90,

          // Stripe
          stripeCustomerId: subscription.stripeCustomerId,
          stripeSubscriptionId: subscription.stripeSubscriptionId,

          // Cupom
          couponCode: subscription.couponCode,
          couponDiscount: subscription.couponDiscount,

          // Faturas recentes
          recentInvoices: subscription.invoices || []
        };
      } else {
        // Empresa sem assinatura
        const userCount = company.users ? company.users.length : 0;

        company.subscriptionInfo = {
          planName: 'Sem Plano',
          status: 'INACTIVE',
          paymentStatus: 'no_subscription',
          hasOverdueInvoices: false,
          overdueAmount: 0,
          nextInvoiceAmount: 0,
          isNearExpiry: false,
          nextDueDate: null,
          daysUntilExpiry: null,
          moduleCount: 0,
          modules: [],
          userCount,
          userLimit: 0,
          userUsagePercentage: 0,
          isNearUserLimit: false,
          billingCycle: null,
          pricePerMonth: 0,
          recentInvoices: []
        };
      }

      res.json(company);
    } catch (error) {
      console.error("Erro ao buscar empresa:", error);
      res.status(500).json({ message: "Erro interno do servidor" });
    }
  }

  /**
   * Lista todas as empresas
   * Para formulário de usuários, utilizar o método listForSelect
   */
  static async list(req, res) {
    try {
      const { page = 1, limit = 10, search, active } = req.query;

      // Verificar se o usuário é SYSTEM_ADMIN para ver todas as empresas
      // Caso contrário, limitar à empresa do usuário
      let where = {
        AND: [
          search
            ? {
                OR: [
                  { name: { contains: search, mode: "insensitive" } },
                  { tradingName: { contains: search, mode: "insensitive" } },
                  { cnpj: { contains: search } },
                ],
              }
            : {},
          active !== undefined ? { active: active === "true" } : {},
        ],
      };

      // Se não for SYSTEM_ADMIN, filtrar apenas pela empresa do usuário
      if (req.user.role !== "SYSTEM_ADMIN") {
        where.AND.push({ id: req.user.companyId });
      }

      const [companies, total] = await Promise.all([
        prisma.company.findMany({
          where,
          skip: (page - 1) * limit,
          take: Number(limit),
          orderBy: { createdAt: "desc" },
          include: {
            documents: {
              where: { type: "LOGO" },
              select: {
                id: true,
                filename: true,
                path: true,
              },
              take: 1,
            },
            subscription: {
              include: {
                modules: {
                  where: { active: true }
                },
                invoices: {
                  orderBy: { createdAt: 'desc' },
                  take: 3, // Últimas 3 faturas
                  select: {
                    id: true,
                    status: true,
                    dueDate: true,
                    paidAt: true,
                    amount: true
                  }
                }
              }
            },
            users: {
              where: { active: true },
              select: { id: true },
            },
          },
        }),
        prisma.company.count({ where }),
      ]);

      // Processar o caminho do logo e informações de assinatura para cada empresa
      companies.forEach(company => {
        // Processar logo
        if (company.documents && company.documents.length > 0) {
          const document = company.documents[0];
          console.log(`[companyController] Processando caminho do logo para empresa ${company.id}:`, document.path);

          // Manter o caminho original completo para referência
          document.originalPath = document.path;

          // Se o caminho começa com /usr/src/app/uploads, manter o caminho completo
          // para que o frontend possa extrair a parte relativa corretamente
          if (document.path.startsWith('/usr/src/app/uploads/')) {
            console.log(`[companyController] Mantendo caminho completo do Docker para empresa ${company.id}`);
          } else {
            // Caso contrário, extrair apenas o nome do arquivo
            const filename = document.path.split('/').pop();
            console.log(`[companyController] Nome do arquivo extraído para empresa ${company.id}:`, filename);
            document.path = filename; // Simplificar para apenas o nome do arquivo
          }
        }

        // Processar informações de assinatura
        if (company.subscription) {
          const subscription = company.subscription;

          // Calcular status de pagamento
          const now = new Date();
          let paymentStatus = 'up_to_date';
          let hasOverdueInvoices = false;

          if (subscription.invoices && subscription.invoices.length > 0) {
            hasOverdueInvoices = subscription.invoices.some(invoice =>
              invoice.status === 'PENDING' && new Date(invoice.dueDate) < now
            );

            if (hasOverdueInvoices) {
              paymentStatus = 'overdue';
            }
          }

          // Calcular próximo vencimento
          let nextDueDate = null;
          if (subscription.nextBillingDate) {
            nextDueDate = subscription.nextBillingDate;
          } else if (subscription.stripeCurrentPeriodEnd) {
            nextDueDate = subscription.stripeCurrentPeriodEnd;
          }

          // Verificar se está próximo do vencimento (próximos 7 dias)
          let isNearExpiry = false;
          if (nextDueDate) {
            const daysUntilExpiry = Math.ceil((new Date(nextDueDate) - now) / (1000 * 60 * 60 * 24));
            isNearExpiry = daysUntilExpiry <= 7 && daysUntilExpiry > 0;
          }

          // Adicionar informações processadas
          company.subscriptionInfo = {
            planName: company.plan || 'Plano Básico',
            status: subscription.status,
            paymentStatus,
            hasOverdueInvoices,
            isNearExpiry,
            nextDueDate,
            moduleCount: subscription.modules ? subscription.modules.length : 0,
            modules: subscription.modules ? subscription.modules.map(m => m.moduleType) : [],
            userCount: company.users ? company.users.length : 0,
            userLimit: subscription.userLimit || 50,
            billingCycle: subscription.billingCycle,
            pricePerMonth: subscription.pricePerMonth
          };
        } else {
          // Empresa sem assinatura
          company.subscriptionInfo = {
            planName: 'Sem Plano',
            status: 'INACTIVE',
            paymentStatus: 'no_subscription',
            hasOverdueInvoices: false,
            isNearExpiry: false,
            nextDueDate: null,
            moduleCount: 0,
            modules: [],
            userCount: company.users ? company.users.length : 0,
            userLimit: 0,
            billingCycle: null,
            pricePerMonth: 0
          };
        }
      });

      res.json({
        companies,
        total,
        pages: Math.ceil(total / limit),
      });
    } catch (error) {
      console.error("Erro ao listar empresas:", error);
      res.status(500).json({ message: "Erro interno do servidor" });
    }
  }

  /**
   * Lista empresas para select no formulário de usuários
   * Versão simplificada com apenas informações básicas
   */
  static async listForSelect(req, res) {
    try {
      // Definir o where baseado no role do usuário
      let where = { active: true };

      // Se não for SYSTEM_ADMIN, limitar à empresa do usuário
      if (req.user.role !== "SYSTEM_ADMIN") {
        where = { ...where, id: req.user.companyId };
      }

      const companies = await prisma.company.findMany({
        where,
        select: {
          id: true,
          name: true,
          tradingName: true,
          cnpj: true,
          active: true,
          city: true,
          state: true,
        },
        orderBy: {
          name: "asc",
        },
      });

      res.json({
        companies,
      });
    } catch (error) {
      console.error("Erro ao listar empresas para select:", error);
      res.status(500).json({ message: "Erro interno do servidor" });
    }
  }

  /**
   * Atualiza uma empresa
   */
  static async update(req, res) {
    try {
      const { id } = req.params;

      // Verificar se o usuário tem permissão para atualizar a empresa
      if (req.user.role !== "SYSTEM_ADMIN" && req.user.companyId !== id) {
        return res.status(403).json({
          message: "Você não tem permissão para atualizar esta empresa",
        });
      }

      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({ errors: errors.array() });
      }

      const {
        name,
        tradingName,
        cnpj,
        phone,
        phone2,
        address,
        city,
        state,
        postalCode,
        website,
        primaryColor,
        secondaryColor,
        description,
        socialMedia,
        businessHours,
      } = req.body;

      // Verificar se a empresa existe
      const existingCompany = await prisma.company.findUnique({
        where: { id },
      });

      if (!existingCompany) {
        return res.status(404).json({ message: "Empresa não encontrada" });
      }

      // Verificar se o CNPJ já está em uso por outra empresa
      if (cnpj && cnpj !== existingCompany.cnpj) {
        const companyWithCnpj = await prisma.company.findUnique({
          where: { cnpj },
        });

        if (companyWithCnpj && companyWithCnpj.id !== id) {
          return res.status(400).json({ message: "CNPJ já cadastrado" });
        }
      }

      const company = await prisma.company.update({
        where: { id },
        data: {
          name,
          tradingName,
          cnpj,
          phone,
          phone2,
          address,
          city,
          state,
          postalCode,
          website,
          primaryColor,
          secondaryColor,
          description,
          socialMedia,
          businessHours,
        },
      });

      // Handle logo file upload if present
      if (req.file) {
        console.log('[companyController] Arquivo de logo detectado:', req.file);
        console.log('[companyController] Caminho do arquivo:', req.file.path);

        // Check if company already has a logo
        const existingLogo = await prisma.document.findFirst({
          where: {
            companyId: id,
            type: "LOGO",
          },
        });

        console.log('[companyController] Logo existente:', existingLogo);

        if (existingLogo) {
          console.log('[companyController] Atualizando logo existente');
          // Update existing logo
          const updatedLogo = await prisma.document.update({
            where: { id: existingLogo.id },
            data: {
              filename: req.file.originalname,
              path: req.file.path,
              mimeType: req.file.mimetype,
              size: req.file.size,
              updatedAt: new Date(),
            },
          });
          console.log('[companyController] Logo atualizado:', updatedLogo);
        } else {
          console.log('[companyController] Criando novo documento de logo');
          // Create new logo document
          const newLogo = await prisma.document.create({
            data: {
              filename: req.file.originalname,
              path: req.file.path,
              type: "LOGO",
              mimeType: req.file.mimetype,
              size: req.file.size,
              ownerType: "COMPANY",
              companyId: id,
              createdById: req.user.id,
            },
          });
          console.log('[companyController] Novo logo criado:', newLogo);
        }
      } else {
        console.log('[companyController] Nenhum arquivo de logo enviado');
      }

      // Get updated company with logo
      const updatedCompany = await prisma.company.findUnique({
        where: { id },
        include: {
          documents: {
            where: { type: "LOGO" },
            select: {
              id: true,
              filename: true,
              path: true,
            },
            take: 1,
          },
        },
      });

      // Processar o caminho do logo para retornar apenas o nome do arquivo
      if (updatedCompany.documents && updatedCompany.documents.length > 0) {
        const document = updatedCompany.documents[0];
        console.log('[companyController] Processando caminho do logo para resposta:', document.path);

        // Extrair apenas o nome do arquivo para simplificar o acesso no frontend
        const filename = document.path.split('/').pop();
        console.log('[companyController] Nome do arquivo extraído:', filename);

        // Atualizar o caminho no objeto de resposta
        document.originalPath = document.path; // Manter o caminho original para referência
        document.path = filename; // Simplificar para apenas o nome do arquivo
      }

      res.json(updatedCompany);
    } catch (error) {
      console.error("Erro ao atualizar empresa:", error);
      res.status(500).json({ message: "Erro interno do servidor" });
    }
  }

  /**
   * Alterna o status de ativo/inativo de uma empresa
   */
  static async toggleStatus(req, res) {
    try {
      const { id } = req.params;

      // Verificar se o usuário é SYSTEM_ADMIN
      if (req.user.role !== "SYSTEM_ADMIN") {
        return res.status(403).json({
          message:
            "Apenas administradores de sistema podem alterar o status de empresas",
        });
      }

      const company = await prisma.company.findUnique({ where: { id } });
      if (!company) {
        return res.status(404).json({ message: "Empresa não encontrada" });
      }

      const updatedCompany = await prisma.company.update({
        where: { id },
        data: { active: !company.active },
      });

      res.json(updatedCompany);
    } catch (error) {
      console.error("Erro ao alterar status da empresa:", error);
      res.status(500).json({ message: "Erro interno do servidor" });
    }
  }

  /**
   * Remove uma empresa
   */
  static async delete(req, res) {
    try {
      const { id } = req.params;

      // Verificar se o usuário é SYSTEM_ADMIN
      if (req.user.role !== "SYSTEM_ADMIN") {
        return res.status(403).json({
          message: "Apenas administradores de sistema podem excluir empresas",
        });
      }

      // Verificar se a empresa existe
      const company = await prisma.company.findUnique({
        where: { id },
        include: {
          emailConfigs: true,
        },
      });

      if (!company) {
        return res.status(404).json({ message: "Empresa não encontrada" });
      }

      // Excluir configurações de email relacionadas
      if (company.emailConfigs.length > 0) {
        await prisma.emailConfig.deleteMany({
          where: { companyId: id },
        });
      }

      // Excluir empresa
      await prisma.company.delete({
        where: { id },
      });

      res.status(204).send();
    } catch (error) {
      console.error("Erro ao excluir empresa:", error);
      res.status(500).json({ message: "Erro interno do servidor" });
    }
  }

  /**
   * Obtém a empresa do usuário autenticado
   * Útil para obter informações da empresa atual sem precisar conhecer o ID
   */
  static async getCurrentCompany(req, res) {
    try {
      // Se o usuário não tem empresa associada
      if (!req.user.companyId) {
        // Para SYSTEM_ADMIN sem empresa, retornar resposta especial
        if (req.user.role === "SYSTEM_ADMIN") {
          return res.json({
            isSystemAdmin: true,
            message:
              "Usuário é um administrador de sistema sem empresa específica",
          });
        }
        return res
          .status(404)
          .json({ message: "Usuário não possui empresa associada" });
      }

      const company = await prisma.company.findUnique({
        where: { id: req.user.companyId },
        include: {
          documents: {
            where: { type: "LOGO" },
            select: {
              id: true,
              filename: true,
              path: true,
              createdAt: true,
            },
            take: 1,
          },
        },
      });

      if (!company) {
        return res.status(404).json({ message: "Empresa não encontrada" });
      }

      res.json(company);
    } catch (error) {
      console.error("Erro ao buscar empresa atual:", error);
      res.status(500).json({ message: "Erro interno do servidor" });
    }
  }

  /**
   * Sincroniza os módulos de todos os usuários de uma empresa com os módulos contratados
   */
  static async syncCompanyModules(req, res) {
    try {
      const { id } = req.params;

      // Verificar se o usuário é SYSTEM_ADMIN
      if (req.user.role !== "SYSTEM_ADMIN") {
        return res.status(403).json({
          message: "Apenas administradores de sistema podem sincronizar módulos",
        });
      }

      // Verificar se a empresa existe
      const company = await prisma.company.findUnique({
        where: { id },
        select: { id: true, name: true, active: true }
      });

      if (!company) {
        return res.status(404).json({ message: "Empresa não encontrada" });
      }

      // Sincronizar módulos de todos os usuários da empresa
      const syncResult = await subscriptionSyncService.syncAllCompanyUsers(id);

      res.json({
        message: `Sincronização concluída para a empresa ${company.name}`,
        company: {
          id: company.id,
          name: company.name
        },
        result: {
          totalUsers: syncResult.success + syncResult.errors,
          successfulSyncs: syncResult.success,
          errors: syncResult.errors,
          details: syncResult.details
        }
      });
    } catch (error) {
      console.error("Erro ao sincronizar módulos da empresa:", error);
      res.status(500).json({ message: "Erro interno do servidor" });
    }
  }
}

module.exports = {
  CompanyController,
  companyValidation,
};
