'use client';

import { useAuth } from '@/contexts/AuthContext';
import { modules } from '@/app/dashboard/components';

/**
 * Hook para gerenciar acesso a módulos
 * @returns {Object} - { hasAccessToModule, accessibleModules }
 */
export const useModules = () => {
  const { user } = useAuth();

  /**
   * Verifica se o usuário tem acesso a um módulo
   * @param {string} moduleRole - Papel necessário para o módulo
   * @returns {boolean} - Se o usuário tem acesso
   */
  const hasAccessToModule = (moduleRole) => {
    // Se o usuário é SYSTEM_ADMIN, tem acesso a tudo
    if (user?.role === 'SYSTEM_ADMIN') {
      return true;
    }

    // Se o usuário é COMPANY_ADMIN, tem acesso a tudo exceto alguns módulos ADMIN
    if (user?.role === 'COMPANY_ADMIN') {
      return true;
    }

    // Módulos BASIC estão disponíveis para todos
    if (moduleRole === 'BASIC') {
      return true;
    }

    // Verificar módulos específicos do usuário
    return user?.modules?.includes(moduleRole);
  };

  // Filtrar módulos acessíveis ao usuário
  const accessibleModules = modules.filter(module => hasAccessToModule(module.role));

  return { hasAccessToModule, accessibleModules };
};