"use client";

import { useState, useEffect, useRef } from 'react';
import { appointmentService } from '@/app/modules/scheduler/services/appointmentService';
import { APPOINTMENT_STATUS } from '../utils/appointmentConstants';
import { utcToLocal } from '@/utils/dateFormatters';

const useAppointmentCalendar = (filters, isDarkMode, permissions) => {
  const calendarRef = useRef(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedDate, setSelectedDate] = useState(null);
  const [selectedAppointment, setSelectedAppointment] = useState(null);
  const [appointments, setAppointments] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [currentView, setCurrentView] = useState("dayGridMonth");

  // Extrair permissões
  const { canCreateAppointment, canEditAppointment, canDeleteAppointment } = permissions;

  // Socket.IO para atualizações em tempo real
  const [socket, setSocket] = useState(null);

  // Inicializar Socket.IO apenas no cliente
  useEffect(() => {
    if (typeof window === 'undefined') return;

    const initSocket = async () => {
      try {
        const token = localStorage.getItem('token');
        if (!token) return;

        // Importação dinâmica para evitar problemas de SSR
        const { default: io } = await import('socket.io-client');

        const socketInstance = io(process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5000', {
          auth: { token },
          reconnectionAttempts: 5,
          reconnectionDelay: 1000,
          autoConnect: true
        });

        socketInstance.on('connect', () => {
          console.log('[CALENDAR-SOCKET] Conectado:', socketInstance.id);
        });

        socketInstance.on('connect_error', (err) => {
          console.error('[CALENDAR-SOCKET] Erro de conexão:', err.message);
        });

        socketInstance.on('disconnect', (reason) => {
          console.log('[CALENDAR-SOCKET] Desconectado:', reason);
        });

        setSocket(socketInstance);
      } catch (error) {
        console.error('[CALENDAR-SOCKET] Erro ao inicializar socket:', error);
      }
    };

    initSocket();

    // Cleanup
    return () => {
      if (socket) {
        socket.disconnect();
        setSocket(null);
      }
    };
  }, []);

  useEffect(() => {
    loadAppointments();
  }, []);

  // Socket.IO listeners para atualizações em tempo real
  useEffect(() => {
    if (!socket || typeof socket.on !== 'function') {
      console.log('[CALENDAR-SOCKET] Socket não disponível ou inválido');
      return;
    }

    console.log('[CALENDAR-SOCKET] Configurando listeners Socket.IO');

    // Listener para agendamentos criados
    const handleAppointmentCreated = (data) => {
      console.log('[CALENDAR-SOCKET] Agendamento(s) criado(s):', data);

      // Recarregar agendamentos para garantir que temos os dados mais atuais
      loadAppointments();
    };

    // Listener para agendamentos atualizados
    const handleAppointmentUpdated = (data) => {
      console.log('[CALENDAR-SOCKET] Agendamento atualizado:', data);

      // Recarregar agendamentos para garantir que temos os dados mais atuais
      loadAppointments();
    };

    // Listener para agendamentos deletados
    const handleAppointmentDeleted = (data) => {
      console.log('[CALENDAR-SOCKET] Agendamento deletado:', data);

      // Recarregar agendamentos para garantir que temos os dados mais atuais
      loadAppointments();
    };

    // Registrar listeners
    socket.on('appointment:created', handleAppointmentCreated);
    socket.on('appointment:updated', handleAppointmentUpdated);
    socket.on('appointment:deleted', handleAppointmentDeleted);

    // Cleanup listeners
    return () => {
      console.log('[CALENDAR-SOCKET] Removendo listeners Socket.IO');
      if (socket && typeof socket.off === 'function') {
        socket.off('appointment:created', handleAppointmentCreated);
        socket.off('appointment:updated', handleAppointmentUpdated);
        socket.off('appointment:deleted', handleAppointmentDeleted);
      }
    };
  }, [socket]);

  // Recarregar os agendamentos quando o modo dark muda para ajustar as cores
  useEffect(() => {
    if (appointments.length > 0) {
      const updatedAppointments = appointments.map((appointment) => ({
        ...appointment,
        backgroundColor: isDarkMode
          ? APPOINTMENT_STATUS[appointment.extendedProps.status || "PENDING"]?.darkColor
          : APPOINTMENT_STATUS[appointment.extendedProps.status || "PENDING"]?.color,
        borderColor: isDarkMode
          ? APPOINTMENT_STATUS[appointment.extendedProps.status || "PENDING"]?.darkColor
          : APPOINTMENT_STATUS[appointment.extendedProps.status || "PENDING"]?.color,
      }));

      setAppointments(updatedAppointments);
    }
  }, [isDarkMode]);

  const loadAppointments = async (searchFilters = filters) => {
    setIsLoading(true);
    try {
      // Para clientes, não enviamos o filtro de providers (profissionais)
      const clientSafeFilters = { ...searchFilters };

      // Se o usuário for cliente, remover o filtro de providers
      if (clientSafeFilters.providers && clientSafeFilters.providers.length > 0) {
        console.log("[CLIENT-FILTER] Removendo filtro de providers para cliente");
        delete clientSafeFilters.providers;
      }

      const response = await appointmentService.getAppointments(clientSafeFilters);

      if (!response?.schedulings && !response?.appointments) {
        console.error("Resposta inválida da API:", response);
        setAppointments([]);
        return;
      }

      const schedulings = response.appointments || [];
      console.log(`[APPOINTMENTS] Carregados ${schedulings.length} agendamentos`);

      const formattedAppointments = schedulings.map((scheduling) => {
        // Usar as datas exatamente como estão no banco de dados
        // Não aplicar nenhuma conversão de fuso horário
        console.log(`[APPOINTMENT] ${scheduling.title} - Original: ${scheduling.startDate}`);

        return {
          id: scheduling.id,
          title: scheduling.title || "",
          start: scheduling.startDate,
          end: scheduling.endDate,
          backgroundColor: isDarkMode
            ? APPOINTMENT_STATUS[scheduling.status || "PENDING"]?.darkColor
            : APPOINTMENT_STATUS[scheduling.status || "PENDING"]?.color,
          borderColor: isDarkMode
            ? APPOINTMENT_STATUS[scheduling.status || "PENDING"]?.darkColor
            : APPOINTMENT_STATUS[scheduling.status || "PENDING"]?.color,
          extendedProps: {
            description: scheduling.description || "",
            personfullName: scheduling.Person?.[0]?.fullName || scheduling.personfullName || "",
            providerfullName: scheduling.provider?.fullName || scheduling.providerfullName || "",
            providerId: scheduling.userId || scheduling.providerId || "",
            // Garantir que o personId seja definido corretamente
            personId: scheduling.Person?.[0]?.id || scheduling.personId || scheduling.clientId || "",
            locationId: scheduling.locationId || "",
            serviceTypefullName: scheduling.serviceType?.name || scheduling.serviceTypefullName|| "",
            serviceTypeId: scheduling.serviceTypeId || "",
            status: scheduling.status || "PENDING",
            // Adicionar informações de convênio, se disponíveis
            insurance: scheduling.insurance || {},
            insuranceId: scheduling.insuranceId || scheduling.insurance?.id || "",
            insuranceInfo: scheduling.insuranceInfo || {},
            // Adicionar objetos completos para uso no modal
            person: scheduling.Person?.[0] || scheduling.person || null,
            serviceType: scheduling.serviceType || null,
          },
        };
      });

      setAppointments(formattedAppointments);
    } catch (error) {
      console.error("Erro ao carregar agendamentos:", error);
      setAppointments([]);
    } finally {
      setIsLoading(false);
    }
  };

  // Função para lidar com slots do calendário
  const handleSlotClassNames = (slotInfo) => {
    // Só aplicamos essa lógica nas visualizações de semana e dia
    if (currentView !== "timeGridWeek" && currentView !== "timeGridDay") {
      return ["min-h-[120px] p-1"];
    }

    return ["min-h-[120px] p-1"];
  };

  // Função para lidar com a seleção de datas
  const handleDateSelect = async (selectInfo, checkProvidersAvailability, setErrorMessage) => {
    // Verificar permissão para criar agendamentos
    if (!canCreateAppointment) {
      setErrorMessage("Você não tem permissão para criar novos agendamentos.");
      return;
    }

    console.log("[DEBUG-SELECT] selectInfo recebido (horário local):", {
      start: selectInfo.start.toLocaleString(),
      end: selectInfo.end?.toLocaleString(),
      dia: selectInfo.start.getDay(),
      hora: selectInfo.start.getHours(),
      view: currentView,
    });

    // Usar os objetos Date diretamente, sem conversão para strings ISO
    // Isso mantém o horário local que o usuário selecionou
    console.log("[DEBUG-DURATION] Antes de calcular end time - selectInfo.end existe?", !!selectInfo.end);
    console.log("[DEBUG-DURATION] FORÇANDO duração padrão de 60 minutos, ignorando o valor de end");

    // Sempre usar 1 hora, independentemente do valor de end que o FullCalendar fornece
    const endTime = new Date(selectInfo.start.getTime() + 60 * 60 * 1000);
    console.log("[DEBUG-DURATION] End time forçado para 1 hora:", endTime.toLocaleString());
    console.log("[DEBUG-DURATION] Duração forçada (minutos):", (endTime - selectInfo.start) / (60 * 1000));

    const selectInfoLocal = {
      ...selectInfo,
      // Garantir que temos objetos Date válidos
      start: selectInfo.start,
      end: endTime,
    };

    console.log("[DEBUG-DURATION] selectInfoLocal final:", {
      start: selectInfoLocal.start.toLocaleString(),
      end: selectInfoLocal.end.toLocaleString(),
      duracaoMinutos: (selectInfoLocal.end - selectInfoLocal.start) / (60 * 1000)
    });

    // Limpa mensagens de erro anteriores
    setErrorMessage(null);

    // Verificação específica para visualizações semanal e diária
    if (
      (currentView === "timeGridWeek" || currentView === "timeGridDay") &&
      filters.providers &&
      filters.providers.length > 0
    ) {
      console.log("[SELECT] Realizando verificação de disponibilidade");

      // Verifica se algum provider está disponível no horário
      const isAnyProviderAvailable = await checkProvidersAvailability(selectInfoLocal);
      console.log("[SELECT] Resultado da verificação:", isAnyProviderAvailable ? "Disponível" : "Indisponível");

      if (!isAnyProviderAvailable) {
        const errorMsg = `Nenhum dos profissionais selecionados está disponível no horário escolhido.
        Por favor, verifique o quadro de horários ou selecione outro horário.`;
        console.log("[SELECT] Exibindo mensagem de erro:", errorMsg);
        setErrorMessage(errorMsg);
        return;
      }
    }

    console.log("[SELECT] Abrindo modal para criar novo agendamento");

    setSelectedDate(selectInfoLocal);
    setSelectedAppointment(null);
    setIsModalOpen(true);
  };

  // Função para lidar com cliques em eventos
  const handleEventClick = async (clickInfo, checkSingleProviderAvailability, setErrorMessage) => {
    // Verificar permissão para editar agendamentos
    if (!canEditAppointment) {
      // Para clientes, apenas mostrar os detalhes sem permitir edição
      const event = clickInfo.event;
      const extendedProps = event.extendedProps || {};

      // Selecionar o agendamento para visualização (sem edição)
      setSelectedAppointment({
        id: event.id,
        title: event.title,
        startDate: event.start,
        endDate: event.end,
        ...extendedProps
      });

      // Abrir o modal (a verificação de permissão para edição será feita no modal)
      setIsModalOpen(true);
      return;
    }

    console.log("[EVENT-CLICK] handleEventClick chamado:", {
      view: currentView,
      eventId: clickInfo.event.id,
      eventTitle: clickInfo.event.title,
      eventStart: clickInfo.event.start?.toLocaleString(),
      eventEnd: clickInfo.event.end?.toLocaleString(),
      providerId: clickInfo.event.extendedProps?.providerId,
      hasProviders: filters.providers?.length > 0,
    });

    const event = clickInfo.event;
    const extendedProps = event.extendedProps || {};
    const providerId = extendedProps.providerId;

    // Verificação de disponibilidade na visualização semanal/diária
    if (
      (currentView === "timeGridWeek" || currentView === "timeGridDay") &&
      filters.providers &&
      filters.providers.length > 0 &&
      providerId
    ) {
      console.log("[EVENT-CLICK] Realizando verificação para evento clicado");

      // Verifica se o providerId do evento está entre os filtrados
      const isProviderInFilters = filters.providers.includes(providerId);
      console.log(`[EVENT-CLICK] Provider está nos filtros? ${isProviderInFilters}`);

      if (isProviderInFilters) {
        // Cria um objeto com formato similar ao selectInfo
        // Usar os objetos Date diretamente, sem conversão para strings ISO
        // SEMPRE usar duração de 1 hora, ignorando o valor de end
        console.log("[EVENT-CLICK-DEBUG] FORÇANDO duração de 60 minutos para o evento");
        const dateInfo = {
          start: event.start,
          end: new Date(event.start.getTime() + 60 * 60 * 1000),
        };
        console.log("[EVENT-CLICK-DEBUG] Duração forçada (minutos):", (dateInfo.end - dateInfo.start) / (60 * 1000));

        // Verifica a disponibilidade
        console.log("[EVENT-CLICK] Verificando disponibilidade para o evento");
        const isAvailable = await checkSingleProviderAvailability(providerId, dateInfo);
        console.log(`[EVENT-CLICK] Evento está dentro do horário de trabalho? ${isAvailable}`);

        if (!isAvailable) {
          const errorMsg = `Este horário está fora do período de trabalho do profissional selecionado.
          Por favor, verifique o quadro de horários ou selecione outro horário.`;
          console.log("[EVENT-CLICK] Exibindo mensagem de erro:", errorMsg);
          setErrorMessage(errorMsg);
          return;
        }
      }
    }

    console.log("[EVENT-CLICK] Abrindo modal para editar agendamento existente");

    // Encontrar o agendamento original nos dados carregados
    // Primeiro, procurar pelo ID exato
    let originalAppointment = appointments.find(a => a.id === event.id);

    // Se não encontrar, tentar buscar pelo ID no extendedProps
    if (!originalAppointment && extendedProps.id) {
      originalAppointment = appointments.find(a => a.id === extendedProps.id);
    }

    // Se ainda não encontrar, usar os dados do evento diretamente
    if (!originalAppointment) {
      console.error("[EVENT-CLICK] Agendamento não encontrado nos dados carregados. ID:", event.id);
      console.log("[EVENT-CLICK] Usando dados do evento diretamente");

      // Extrair dados diretamente do evento do calendário
      const fallbackAppointment = {
        id: event.id,
        title: event.title,
        description: extendedProps.description || "",
        providerId: extendedProps.providerId || "",
        personId: extendedProps.personId || "",
        locationId: extendedProps.locationId || "",
        serviceTypeId: extendedProps.serviceTypeId || "",
        insuranceId: extendedProps.insuranceId || extendedProps.insurance?.id || "",
        startDate: event.start ? event.start.toISOString() : new Date().toISOString(),
        endDate: event.end ? event.end.toISOString() : new Date().toISOString(),
        status: extendedProps.status || "PENDING",
        // Adicionar objetos completos para uso no modal
        insurance: extendedProps.insurance || null,
        serviceType: extendedProps.serviceType || null,
        person: extendedProps.person || null,
        location: extendedProps.location || null,
        // Adicionar extendedProps completo para debug
        extendedProps: extendedProps
      };

      console.log("[EVENT-CLICK] Dados do evento usados como fallback:", fallbackAppointment);
      setSelectedAppointment(fallbackAppointment);
      setIsModalOpen(true);
      return;
    }

    console.log("[EVENT-CLICK] Agendamento original encontrado:", originalAppointment);

    // Extrair o personId do array Person se existir
    const personId = originalAppointment.Person?.[0]?.id ||
                    originalAppointment.person?.id ||
                    originalAppointment.personId ||
                    originalAppointment.clientId ||
                    extendedProps.personId ||
                    "";

    // Criar o objeto de agendamento combinando dados do original e do evento
    const appointment = {
      id: originalAppointment.id || event.id,
      title: originalAppointment.title || event.title || "",
      description: originalAppointment.description || extendedProps.description || "",
      providerId: originalAppointment.userId || originalAppointment.providerId || extendedProps.providerId || "",
      personId: personId,
      locationId: originalAppointment.locationId || extendedProps.locationId || "",
      serviceTypeId: originalAppointment.serviceTypeId || extendedProps.serviceTypeId || "",
      insuranceId: originalAppointment.insuranceId || extendedProps.insuranceId || "",
      startDate: originalAppointment.startDate || (event.start ? event.start.toISOString() : new Date().toISOString()),
      endDate: originalAppointment.endDate || (event.end ? event.end.toISOString() : new Date().toISOString()),
      status: originalAppointment.status || extendedProps.status || "PENDING",
      // Adicionar objetos completos para uso no modal
      insurance: originalAppointment.insurance || extendedProps.insurance || null,
      serviceType: originalAppointment.serviceType || extendedProps.serviceType || null,
      person: originalAppointment.Person?.[0] || originalAppointment.person || extendedProps.person || null,
      location: originalAppointment.location || extendedProps.location || null,
      // Adicionar extendedProps completo para debug
      extendedProps: extendedProps
    };

    // Log detalhado para depuração
    console.log("[EVENT-CLICK] Dados do personId:", {
      fromPerson: originalAppointment.Person?.[0]?.id,
      fromPersonObj: originalAppointment.person?.id,
      fromPersonId: originalAppointment.personId,
      fromClientId: originalAppointment.clientId,
      fromExtendedProps: extendedProps.personId,
      final: appointment.personId
    });

    console.log("[EVENT-CLICK] Agendamento criado a partir dos dados existentes:", {
      id: appointment.id,
      title: appointment.title,
      personId: appointment.personId,
      insuranceId: appointment.insuranceId,
      serviceTypeId: appointment.serviceTypeId,
      startDate: appointment.startDate,
      endDate: appointment.endDate
    });

    // Abrir o modal de agendamento com os dados do evento
    setSelectedAppointment(appointment);
    setIsModalOpen(true);
  };

  // Registra alterações na visualização do calendário
  const handleDatesSet = (dateInfo) => {
    console.log("[CALENDAR] datesSet:", dateInfo.view.type);
    setCurrentView(dateInfo.view.type);
  };

  // Pesquisa de agendamentos
  const handleSearch = async (searchFilters) => {
    console.log("[SEARCH] handleSearch chamado com filtros:", searchFilters);
    await loadAppointments(searchFilters);
  };

  return {
    calendarRef,
    isModalOpen,
    setIsModalOpen,
    selectedDate,
    setSelectedDate,
    selectedAppointment,
    setSelectedAppointment,
    appointments,
    isLoading,
    currentView,
    handleSlotClassNames,
    handleDateSelect,
    handleEventClick,
    handleDatesSet,
    handleSearch,
    loadAppointments
  };
};

export default useAppointmentCalendar;