"use client";

import { useState } from "react";
import { InputMask } from "@react-input/mask";
import {
  UserPlus,
  Mail,
  Lock,
  User,
  Phone,
  MapPin,
  CreditCard,
  Calendar,
  Building,
  CheckCircle,
  ArrowRight,
  ArrowLeft
} from "lucide-react";
import { useRouter } from "next/navigation";
import Link from "next/link";
import { cn } from "@/lib/utils";
import { api } from "@/utils/api";

export default function SubscriptionSignupPage() {
  const router = useRouter();
  const [currentStep, setCurrentStep] = useState(1);
  const [documentType, setDocumentType] = useState("cpf");
  
  const [formData, setFormData] = useState({
    // Dados pessoais
    login: "",
    fullName: "",
    email: "",
    password: "",
    confirmPassword: "",
    cpf: "",
    cnpj: "",
    birthDate: "",
    address: "",
    phone: "",
    
    // Dados da empresa
    companyName: "",
    companyTradingName: "",
    companyCnpj: "",
    companyPhone: "",
    companyAddress: "",
    companyCity: "",
    companyState: "",
    companyPostalCode: ""
  });

  const [errors, setErrors] = useState({});
  const [isLoading, setIsLoading] = useState(false);

  // Detecta se está em ambiente de desenvolvimento (localhost)
  const isDevelopment = typeof window !== 'undefined' &&
    (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1');

  // Dados de teste para desenvolvimento
  const devData = {
    login: "teste_dev",
    fullName: "Usuário de Teste",
    email: "<EMAIL>",
    password: "123456",
    confirmPassword: "123456",
    cpf: "123.456.789-00",
    cnpj: "12.345.678/0001-90",
    birthDate: "1990-01-01",
    address: "Rua de Teste, 123",
    phone: "(11) 99999-9999",
    companyName: "Empresa de Teste Ltda",
    companyTradingName: "Teste Corp",
    companyCnpj: "98.765.432/0001-10",
    companyPhone: "(11) 88888-8888",
    companyAddress: "Av. Empresarial, 456",
    companyCity: "São Paulo",
    companyState: "SP",
    companyPostalCode: "01234-567"
  };

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
    // Limpa o erro do campo quando o usuário começa a digitar
    if (errors[name]) {
      setErrors((prev) => ({
        ...prev,
        [name]: undefined,
      }));
    }
  };

  const removeMask = (value) => {
    return value ? value.replace(/\D/g, "") : "";
  };

  // Função para preencher dados de desenvolvimento
  const fillDevData = () => {
    if (isDevelopment) {
      setFormData(devData);
    }
  };

  // Função para pular para o próximo passo (apenas em desenvolvimento)
  const skipToStep = (step) => {
    if (isDevelopment) {
      if (step >= 2 && currentStep === 1) {
        fillDevData();
      }
      setCurrentStep(step);
    }
  };

  const validateStep1 = () => {
    const newErrors = {};

    if (!formData.login) newErrors.login = "Login é obrigatório";
    if (!formData.fullName) newErrors.fullName = "Nome é obrigatório";
    if (!formData.email || !/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = "Email inválido";
    }
    if (!formData.password || formData.password.length < 6) {
      newErrors.password = "Senha deve ter no mínimo 6 caracteres";
    }
    if (formData.password !== formData.confirmPassword) {
      newErrors.confirmPassword = "Senhas não conferem";
    }

    if (documentType === "cpf" && !formData.cpf) {
      newErrors.cpf = "CPF é obrigatório";
    }
    if (documentType === "cnpj" && !formData.cnpj) {
      newErrors.cnpj = "CNPJ é obrigatório";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const validateStep2 = () => {
    const newErrors = {};

    if (!formData.companyName) newErrors.companyName = "Nome da empresa é obrigatório";
    if (!formData.companyCnpj) newErrors.companyCnpj = "CNPJ da empresa é obrigatório";
    if (!formData.companyPhone) newErrors.companyPhone = "Telefone da empresa é obrigatório";

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleNextStep = () => {
    if (currentStep === 1 && validateStep1()) {
      setCurrentStep(2);
    } else if (currentStep === 2 && validateStep2()) {
      setCurrentStep(3);
    }
  };

  const handlePrevStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  // NOVA FUNÇÃO: Solicitar confirmação de email e redirecionar
  const handleSubmit = async () => {
    if (!validateStep2()) return;

    setIsLoading(true);
    try {
      // 1. Solicitar confirmação de email com nome do usuário
      await api.post("/auth/request-email-confirmation", {
        email: formData.email,
        userName: formData.fullName // ✅ Adicionar nome para email mais personalizado
      });

      // 2. Salvar dados do cadastro no localStorage temporariamente
      const signupData = {
        // Dados pessoais
        login: formData.login,
        fullName: formData.fullName,
        email: formData.email,
        password: formData.password,
        cpf: documentType === "cpf" ? formData.cpf : undefined,
        cnpj: documentType === "cnpj" ? formData.cnpj : undefined,
        birthDate: formData.birthDate || undefined,
        address: formData.address || undefined,
        phone: formData.phone || undefined,

        // Dados da empresa
        companyName: formData.companyName,
        companyTradingName: formData.companyTradingName,
        companyCnpj: formData.companyCnpj,
        companyPhone: formData.companyPhone,
        companyAddress: formData.companyAddress,
        companyCity: formData.companyCity,
        companyState: formData.companyState,
        companyPostalCode: formData.companyPostalCode,

        timestamp: Date.now()
      };

      localStorage.setItem('pendingSignup', JSON.stringify(signupData));

      // 3. Redirecionar para página de confirmação de email
      router.push(`/subscription/signup/confirm-email?email=${encodeURIComponent(formData.email)}`);

    } catch (error) {
      setErrors((prev) => ({
        ...prev,
        submit: error.response?.data?.message || "Erro ao enviar confirmação de email",
      }));
    } finally {
      setIsLoading(false);
    }
  };

  const inputClasses = "block w-full pl-10 pr-3 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500 text-gray-900 dark:text-white bg-white dark:bg-gray-700";
  const labelClasses = "block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1";
  const iconContainerClasses = "absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none";

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center p-4">
      <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-xl w-full max-w-4xl p-8">
        {/* Header */}
        <div className="text-center mb-8">
          <div className="flex justify-center items-center mb-4">
            <UserPlus className="h-12 w-12 text-orange-500" />
            <h2 className="mx-4 text-3xl font-bold text-gray-900 dark:text-white">
              Criar conta e assinar
            </h2>
          </div>

          {/* Progress Steps */}
          <div className="flex justify-center items-center space-x-4 mb-6">
            {[1, 2, 3].map((step) => (
              <div key={step} className="flex items-center">
                <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
                  currentStep >= step
                    ? 'bg-orange-500 text-white'
                    : 'bg-gray-200 dark:bg-gray-600 text-gray-600 dark:text-gray-300'
                }`}>
                  {currentStep > step ? <CheckCircle className="w-5 h-5" /> : step}
                </div>
                {step < 3 && (
                  <div className={`w-12 h-1 mx-2 ${
                    currentStep > step ? 'bg-orange-500' : 'bg-gray-200 dark:bg-gray-600'
                  }`} />
                )}
              </div>
            ))}
          </div>

          <div className="text-sm text-gray-600 dark:text-gray-400">
            {currentStep === 1 && "Dados pessoais"}
            {currentStep === 2 && "Dados da empresa"}
            {currentStep === 3 && "Confirmação e trial"}
          </div>

          {/* Botões de Desenvolvimento (apenas em localhost) */}
          {isDevelopment && (
            <div className="mt-4 p-3 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg">
              <p className="text-xs text-yellow-800 dark:text-yellow-300 mb-2 text-center">
                🚧 Modo Desenvolvimento - Atalhos para testes
              </p>
              <div className="flex justify-center gap-2">
                <button
                  type="button"
                  onClick={fillDevData}
                  className="px-3 py-1 bg-yellow-500 text-white rounded text-xs hover:bg-yellow-600"
                >
                  Preencher Dados
                </button>
                <button
                  type="button"
                  onClick={() => skipToStep(1)}
                  className={`px-3 py-1 rounded text-xs ${currentStep === 1 ? 'bg-blue-500 text-white' : 'bg-gray-200 text-gray-700 hover:bg-gray-300'}`}
                >
                  Passo 1
                </button>
                <button
                  type="button"
                  onClick={() => skipToStep(2)}
                  className={`px-3 py-1 rounded text-xs ${currentStep === 2 ? 'bg-blue-500 text-white' : 'bg-gray-200 text-gray-700 hover:bg-gray-300'}`}
                >
                  Passo 2
                </button>
                <button
                  type="button"
                  onClick={() => skipToStep(3)}
                  className={`px-3 py-1 rounded text-xs ${currentStep === 3 ? 'bg-blue-500 text-white' : 'bg-gray-200 text-gray-700 hover:bg-gray-300'}`}
                >
                  Passo 3
                </button>
              </div>
            </div>
          )}
        </div>

        {errors.submit && (
          <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 text-red-600 dark:text-red-400 px-4 py-3 rounded-lg mb-6">
            {errors.submit}
          </div>
        )}

        {/* Step 1: Dados Pessoais */}
        {currentStep === 1 && (
          <div className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Login */}
              <div>
                <label className={labelClasses} htmlFor="login">Login</label>
                <div className="relative">
                  <div className={iconContainerClasses}>
                    <User className="h-5 w-5 text-gray-400 dark:text-gray-500" />
                  </div>
                  <input
                    id="login"
                    name="login"
                    type="text"
                    value={formData.login}
                    onChange={handleChange}
                    required
                    className={cn(inputClasses, errors.login && "border-red-500")}
                    placeholder="Seu login"
                    disabled={isLoading}
                  />
                </div>
                {errors.login && <p className="mt-1 text-xs text-red-600">{errors.login}</p>}
              </div>

              {/* Nome Completo */}
              <div>
                <label className={labelClasses} htmlFor="fullName">Nome Completo</label>
                <div className="relative">
                  <div className={iconContainerClasses}>
                    <User className="h-5 w-5 text-gray-400" />
                  </div>
                  <input
                    id="fullName"
                    name="fullName"
                    type="text"
                    value={formData.fullName}
                    onChange={handleChange}
                    required
                    className={cn(inputClasses, errors.fullName && "border-red-500")}
                    placeholder="Seu nome completo"
                    disabled={isLoading}
                  />
                </div>
                {errors.fullName && <p className="mt-1 text-xs text-red-600">{errors.fullName}</p>}
              </div>
            </div>

            {/* Email e Senha */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className={labelClasses} htmlFor="email">Email</label>
                <div className="relative">
                  <div className={iconContainerClasses}>
                    <Mail className="h-5 w-5 text-gray-400" />
                  </div>
                  <input
                    id="email"
                    name="email"
                    type="email"
                    value={formData.email}
                    onChange={handleChange}
                    required
                    className={cn(inputClasses, errors.email && "border-red-500")}
                    placeholder="<EMAIL>"
                    disabled={isLoading}
                  />
                </div>
                {errors.email && <p className="mt-1 text-xs text-red-600">{errors.email}</p>}
              </div>

              <div>
                <label className={labelClasses} htmlFor="password">Senha</label>
                <div className="relative">
                  <div className={iconContainerClasses}>
                    <Lock className="h-5 w-5 text-gray-400" />
                  </div>
                  <input
                    id="password"
                    name="password"
                    type="password"
                    value={formData.password}
                    onChange={handleChange}
                    required
                    className={cn(inputClasses, errors.password && "border-red-500")}
                    placeholder="••••••••"
                    disabled={isLoading}
                  />
                </div>
                {errors.password && <p className="mt-1 text-xs text-red-600">{errors.password}</p>}
              </div>
            </div>

            {/* Confirmar Senha e Tipo de Documento */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className={labelClasses} htmlFor="confirmPassword">Confirmar Senha</label>
                <div className="relative">
                  <div className={iconContainerClasses}>
                    <Lock className="h-5 w-5 text-gray-400" />
                  </div>
                  <input
                    id="confirmPassword"
                    name="confirmPassword"
                    type="password"
                    value={formData.confirmPassword}
                    onChange={handleChange}
                    required
                    className={cn(inputClasses, errors.confirmPassword && "border-red-500")}
                    placeholder="••••••••"
                    disabled={isLoading}
                  />
                </div>
                {errors.confirmPassword && <p className="mt-1 text-xs text-red-600">{errors.confirmPassword}</p>}
              </div>

              <div>
                <label className={labelClasses}>Tipo de Documento</label>
                <div className="flex space-x-4 pt-2">
                  <label className="flex items-center">
                    <input
                      type="radio"
                      name="documentType"
                      value="cpf"
                      checked={documentType === "cpf"}
                      onChange={(e) => setDocumentType(e.target.value)}
                      className="mr-2"
                    />
                    CPF
                  </label>
                  <label className="flex items-center">
                    <input
                      type="radio"
                      name="documentType"
                      value="cnpj"
                      checked={documentType === "cnpj"}
                      onChange={(e) => setDocumentType(e.target.value)}
                      className="mr-2"
                    />
                    CNPJ
                  </label>
                </div>
              </div>
            </div>

            {/* Documento e Telefone */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className={labelClasses} htmlFor={documentType}>
                  {documentType === "cpf" ? "CPF" : "CNPJ"}
                </label>
                <div className="relative">
                  <div className={iconContainerClasses}>
                    <CreditCard className="h-5 w-5 text-gray-400" />
                  </div>
                  <InputMask
                    mask={documentType === "cpf" ? "___.___.___-__" : "__.___.___/____-__"}
                    replacement={{ _: /\d/ }}
                    name={documentType}
                    value={formData[documentType]}
                    onChange={handleChange}
                    className={cn(inputClasses, errors[documentType] && "border-red-500")}
                    placeholder={documentType === "cpf" ? "000.000.000-00" : "00.000.000/0000-00"}
                    disabled={isLoading}
                  />
                </div>
                {errors[documentType] && <p className="mt-1 text-xs text-red-600">{errors[documentType]}</p>}
              </div>

              <div>
                <label className={labelClasses} htmlFor="phone">Telefone (opcional)</label>
                <div className="relative">
                  <div className={iconContainerClasses}>
                    <Phone className="h-5 w-5 text-gray-400" />
                  </div>
                  <InputMask
                    mask="(__) _____-____"
                    replacement={{ _: /\d/ }}
                    name="phone"
                    value={formData.phone}
                    onChange={handleChange}
                    className={inputClasses}
                    placeholder="(11) 99999-9999"
                    disabled={isLoading}
                  />
                </div>
              </div>
            </div>

            {/* Data de Nascimento e Endereço */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className={labelClasses} htmlFor="birthDate">Data de Nascimento (opcional)</label>
                <div className="relative">
                  <div className={iconContainerClasses}>
                    <Calendar className="h-5 w-5 text-gray-400" />
                  </div>
                  <input
                    id="birthDate"
                    name="birthDate"
                    type="date"
                    value={formData.birthDate}
                    onChange={handleChange}
                    className={inputClasses}
                    disabled={isLoading}
                  />
                </div>
              </div>

              <div>
                <label className={labelClasses} htmlFor="address">Endereço (opcional)</label>
                <div className="relative">
                  <div className={iconContainerClasses}>
                    <MapPin className="h-5 w-5 text-gray-400" />
                  </div>
                  <input
                    id="address"
                    name="address"
                    type="text"
                    value={formData.address}
                    onChange={handleChange}
                    className={inputClasses}
                    placeholder="Seu endereço"
                    disabled={isLoading}
                  />
                </div>
              </div>
            </div>

            <div className="flex justify-between pt-6">
              <Link
                href="/landing"
                className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
              >
                <ArrowLeft className="w-4 h-4 mr-2" />
                Voltar
              </Link>
              
              <button
                type="button"
                onClick={handleNextStep}
                className="inline-flex items-center px-6 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-orange-500 hover:bg-orange-600"
              >
                Próximo
                <ArrowRight className="w-4 h-4 ml-2" />
              </button>
            </div>
          </div>
        )}

        {/* Step 2: Dados da Empresa */}
        {currentStep === 2 && (
          <div className="space-y-6">
            {/* Nome da Empresa e Nome Fantasia */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className={labelClasses} htmlFor="companyName">Nome da Empresa</label>
                <div className="relative">
                  <div className={iconContainerClasses}>
                    <Building className="h-5 w-5 text-gray-400" />
                  </div>
                  <input
                    id="companyName"
                    name="companyName"
                    type="text"
                    value={formData.companyName}
                    onChange={handleChange}
                    required
                    className={cn(inputClasses, errors.companyName && "border-red-500")}
                    placeholder="Razão social da empresa"
                    disabled={isLoading}
                  />
                </div>
                {errors.companyName && <p className="mt-1 text-xs text-red-600">{errors.companyName}</p>}
              </div>

              <div>
                <label className={labelClasses} htmlFor="companyTradingName">Nome Fantasia (opcional)</label>
                <div className="relative">
                  <div className={iconContainerClasses}>
                    <Building className="h-5 w-5 text-gray-400" />
                  </div>
                  <input
                    id="companyTradingName"
                    name="companyTradingName"
                    type="text"
                    value={formData.companyTradingName}
                    onChange={handleChange}
                    className={inputClasses}
                    placeholder="Nome fantasia"
                    disabled={isLoading}
                  />
                </div>
              </div>
            </div>

            {/* CNPJ e Telefone da Empresa */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className={labelClasses} htmlFor="companyCnpj">CNPJ da Empresa</label>
                <div className="relative">
                  <div className={iconContainerClasses}>
                    <CreditCard className="h-5 w-5 text-gray-400" />
                  </div>
                  <InputMask
                    mask="__.___.___/____-__"
                    replacement={{ _: /\d/ }}
                    name="companyCnpj"
                    value={formData.companyCnpj}
                    onChange={handleChange}
                    className={cn(inputClasses, errors.companyCnpj && "border-red-500")}
                    placeholder="00.000.000/0000-00"
                    disabled={isLoading}
                  />
                </div>
                {errors.companyCnpj && <p className="mt-1 text-xs text-red-600">{errors.companyCnpj}</p>}
              </div>

              <div>
                <label className={labelClasses} htmlFor="companyPhone">Telefone da Empresa</label>
                <div className="relative">
                  <div className={iconContainerClasses}>
                    <Phone className="h-5 w-5 text-gray-400" />
                  </div>
                  <InputMask
                    mask="(__) _____-____"
                    replacement={{ _: /\d/ }}
                    name="companyPhone"
                    value={formData.companyPhone}
                    onChange={handleChange}
                    className={cn(inputClasses, errors.companyPhone && "border-red-500")}
                    placeholder="(11) 99999-9999"
                    disabled={isLoading}
                  />
                </div>
                {errors.companyPhone && <p className="mt-1 text-xs text-red-600">{errors.companyPhone}</p>}
              </div>
            </div>

            {/* Endereço da Empresa */}
            <div>
              <label className={labelClasses} htmlFor="companyAddress">Endereço da Empresa (opcional)</label>
              <div className="relative">
                <div className={iconContainerClasses}>
                  <MapPin className="h-5 w-5 text-gray-400" />
                </div>
                <input
                  id="companyAddress"
                  name="companyAddress"
                  type="text"
                  value={formData.companyAddress}
                  onChange={handleChange}
                  className={inputClasses}
                  placeholder="Endereço completo da empresa"
                  disabled={isLoading}
                />
              </div>
            </div>

            {/* Cidade, Estado e CEP */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div>
                <label className={labelClasses} htmlFor="companyCity">Cidade (opcional)</label>
                <input
                  id="companyCity"
                  name="companyCity"
                  type="text"
                  value={formData.companyCity}
                  onChange={handleChange}
                  className={inputClasses}
                  placeholder="Cidade"
                  disabled={isLoading}
                />
              </div>

              <div>
                <label className={labelClasses} htmlFor="companyState">Estado (opcional)</label>
                <input
                  id="companyState"
                  name="companyState"
                  type="text"
                  value={formData.companyState}
                  onChange={handleChange}
                  className={inputClasses}
                  placeholder="Estado"
                  disabled={isLoading}
                />
              </div>

              <div>
                <label className={labelClasses} htmlFor="companyPostalCode">CEP (opcional)</label>
                <InputMask
                  mask="_____-___"
                  replacement={{ _: /\d/ }}
                  name="companyPostalCode"
                  value={formData.companyPostalCode}
                  onChange={handleChange}
                  className={inputClasses}
                  placeholder="00000-000"
                  disabled={isLoading}
                />
              </div>
            </div>

            <div className="flex justify-between pt-6">
              <button
                type="button"
                onClick={handlePrevStep}
                className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
              >
                <ArrowLeft className="w-4 h-4 mr-2" />
                Anterior
              </button>

              <button
                type="button"
                onClick={handleNextStep}
                className="inline-flex items-center px-6 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-orange-500 hover:bg-orange-600"
              >
                Próximo
                <ArrowRight className="w-4 h-4 ml-2" />
              </button>
            </div>
          </div>
        )}

        {/* Step 3: Confirmação e Trial */}
        {currentStep === 3 && (
          <div className="space-y-6">
            {/* Aviso sobre Trial */}
            <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-6">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <CheckCircle className="h-8 w-8 text-green-400" />
                </div>
                <div className="ml-3">
                  <h3 className="text-lg font-medium text-green-800 dark:text-green-300">
                    Período de Trial Gratuito
                  </h3>
                  <div className="mt-2 text-sm text-green-700 dark:text-green-400">
                    <p>
                      Sua conta será criada com <strong>30 dias de trial gratuito</strong> para você experimentar 
                      todas as funcionalidades do sistema. Não é necessário cartão de crédito neste momento.
                    </p>
                  </div>
                </div>
              </div>
            </div>

            {/* Resumo da Conta */}
            <div className="bg-white dark:bg-gray-700 rounded-xl border-2 border-gray-200 dark:border-gray-600 p-6">
              <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                Resumo da sua conta
              </h4>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Dados Pessoais */}
                <div>
                  <h5 className="font-medium text-gray-700 dark:text-gray-300 mb-3">Dados Pessoais</h5>
                  <div className="space-y-2 text-sm">
                    <div><span className="text-gray-500">Nome:</span> <span className="font-medium">{formData.fullName}</span></div>
                    <div><span className="text-gray-500">Email:</span> <span className="font-medium">{formData.email}</span></div>
                    <div><span className="text-gray-500">Login:</span> <span className="font-medium">{formData.login}</span></div>
                    {formData[documentType] && (
                      <div><span className="text-gray-500">{documentType.toUpperCase()}:</span> <span className="font-medium">{formData[documentType]}</span></div>
                    )}
                  </div>
                </div>

                {/* Dados da Empresa */}
                <div>
                  <h5 className="font-medium text-gray-700 dark:text-gray-300 mb-3">Dados da Empresa</h5>
                  <div className="space-y-2 text-sm">
                    <div><span className="text-gray-500">Empresa:</span> <span className="font-medium">{formData.companyName}</span></div>
                    {formData.companyTradingName && (
                      <div><span className="text-gray-500">Nome Fantasia:</span> <span className="font-medium">{formData.companyTradingName}</span></div>
                    )}
                    <div><span className="text-gray-500">CNPJ:</span> <span className="font-medium">{formData.companyCnpj}</span></div>
                    <div><span className="text-gray-500">Telefone:</span> <span className="font-medium">{formData.companyPhone}</span></div>
                  </div>
                </div>
              </div>
            </div>

            {/* Informações sobre o Trial */}
            <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-6">
              <h5 className="font-semibold text-blue-900 dark:text-blue-300 mb-3">O que está incluído no trial:</h5>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                <div className="flex items-center">
                  <CheckCircle className="w-4 h-4 text-blue-500 mr-2" />
                  <span className="text-sm text-blue-700 dark:text-blue-300">Sistema de Agendamento</span>
                </div>
                <div className="flex items-center">
                  <CheckCircle className="w-4 h-4 text-blue-500 mr-2" />
                  <span className="text-sm text-blue-700 dark:text-blue-300">Gestão de Pacientes</span>
                </div>
                <div className="flex items-center">
                  <CheckCircle className="w-4 h-4 text-blue-500 mr-2" />
                  <span className="text-sm text-blue-700 dark:text-blue-300">Calendário Integrado</span>
                </div>
                <div className="flex items-center">
                  <CheckCircle className="w-4 h-4 text-blue-500 mr-2" />
                  <span className="text-sm text-blue-700 dark:text-blue-300">Notificações Automáticas</span>
                </div>
                <div className="flex items-center">
                  <CheckCircle className="w-4 h-4 text-blue-500 mr-2" />
                  <span className="text-sm text-blue-700 dark:text-blue-300">Até 50 usuários</span>
                </div>
                <div className="flex items-center">
                  <CheckCircle className="w-4 h-4 text-blue-500 mr-2" />
                  <span className="text-sm text-blue-700 dark:text-blue-300">Suporte técnico</span>
                </div>
              </div>
              
              <div className="mt-4 p-3 bg-blue-100 dark:bg-blue-900/30 rounded-lg">
                <p className="text-sm text-blue-800 dark:text-blue-300 text-center">
                  <strong>Após o trial:</strong> Você poderá escolher o plano ideal e configurar módulos adicionais 
                  como Financeiro, RH e ABA+ diretamente no dashboard.
                </p>
              </div>
            </div>

            <div className="flex justify-between pt-6">
              <button
                type="button"
                onClick={handlePrevStep}
                className="inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600"
              >
                <ArrowLeft className="w-4 h-4 mr-2" />
                Anterior
              </button>

              <button
                type="button"
                onClick={handleSubmit}
                disabled={isLoading}
                className="inline-flex items-center px-8 py-3 border border-transparent rounded-md shadow-sm text-base font-medium text-white bg-green-600 hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isLoading ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    Enviando confirmação...
                  </>
                ) : (
                  <>
                    Confirmar e Criar Conta
                    <Mail className="w-5 h-5 ml-2" />
                  </>
                )}
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}