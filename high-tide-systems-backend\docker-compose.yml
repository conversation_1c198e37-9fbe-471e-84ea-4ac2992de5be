services:
  postgres:
    image: postgres:latest
    container_name: high-tide-systems-db
    restart: unless-stopped
    environment:
      POSTGRES_USER: ${DB_USER:-admin}
      POSTGRES_PASSWORD: ${DB_PASSWORD:-changeme_in_production}
      POSTGRES_DB: ${DB_NAME:-hightidesystems}
    ports:
      - "${DB_PORT:-5432}:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${DB_USER:-admin} -d ${DB_NAME:-hightidesystems}"]
      interval: 5s
      timeout: 5s
      retries: 5
    networks:
      - app-network
  redis:
    image: redis:alpine
    container_name: high-tide-systems-redis
    restart: unless-stopped
    ports:
      - "${REDIS_PORT:-6379}:6379"
    volumes:
      - redis_data:/data
    networks:
      - app-network
  api:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: high-tide-systems-api
    restart: unless-stopped
    depends_on:
      postgres:
        condition: service_healthy
    environment:
      - DATABASE_URL=postgresql://${DB_USER:-admin}:${DB_PASSWORD:-changeme_in_production}@postgres:5432/${DB_NAME:-hightidesystems}?schema=public
      - JWT_SECRET=${JWT_SECRET:-changeme_in_production}
      - JWT_EXPIRY=${JWT_EXPIRY:-24h}
      - PORT=${PORT:-5000}
      - NODE_ENV=${NODE_ENV:-development}
      - EMAIL_HOST=${EMAIL_HOST}
      - EMAIL_PORT=${EMAIL_PORT}
      - EMAIL_SECURE=${EMAIL_SECURE:-false}
      - EMAIL_USER=${EMAIL_USER}
      - EMAIL_PASSWORD=${EMAIL_PASSWORD}
      - EMAIL_NAME=${EMAIL_NAME:-High Tide Systems}
      - EMAIL_TLS_REJECT_UNAUTHORIZED=${EMAIL_TLS_REJECT_UNAUTHORIZED:-false}
      - FRONTEND_URL=${FRONTEND_URL:-http://localhost:3000}
      - REDIS_URL=redis://redis:6379
      - CORS_ORIGIN=${CORS_ORIGIN:-http://localhost:3000}
      - UPLOAD_PATH=/usr/src/app/uploads
      - BCRYPT_SALT_ROUNDS=${BCRYPT_SALT_ROUNDS:-12}
      - MAX_LOGIN_ATTEMPTS=${MAX_LOGIN_ATTEMPTS:-5}
    ports:
      - "${PORT:-5000}:5000"
    volumes:
      - ./:/usr/src/app
      - /usr/src/app/node_modules
      - uploads_data:/usr/src/app/uploads
    command: sh -c "npx prisma migrate deploy && npx prisma generate && nodemon --legacy-watch src/server.js"
    networks:
      - app-network
  studio:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: high-tide-systems-studio
    restart: unless-stopped
    depends_on:
      postgres:
        condition: service_healthy
    environment:
      - DATABASE_URL=postgresql://${DB_USER:-admin}:${DB_PASSWORD:-changeme_in_production}@postgres:5432/${DB_NAME:-hightidesystems}?schema=public
    ports:
      - "5555:5555"
    volumes:
      - ./:/usr/src/app
      - /usr/src/app/node_modules
    command: npx prisma studio
    networks:
      - app-network
volumes:
  postgres_data:
  redis_data:
  uploads_data:
networks:
  app-network:
    driver: bridge