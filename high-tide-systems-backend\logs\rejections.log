{"timestamp":"2025-06-16 18:07:14","level":"error","message":"unhandledRejection: The client is closed\nError: The client is closed\n    at Commander._RedisClient_sendCommand (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:520:31)\n    at Commander.commandsExecutor (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:190:154)\n    at BaseClass.<computed> [as publish] (/usr/src/app/node_modules/@redis/client/dist/lib/commander.js:8:29)\n    at RedisAdapter.broadcast (/usr/src/app/node_modules/@socket.io/redis-adapter/dist/index.js:473:28)\n    at BroadcastOperator.emit (/usr/src/app/node_modules/socket.io/dist/broadcast-operator.js:169:26)\n    at updateUserStatus (/usr/src/app/src/socket/socketService.js:212:44)","error":{},"stack":"Error: The client is closed\n    at Commander._RedisClient_sendCommand (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:520:31)\n    at Commander.commandsExecutor (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:190:154)\n    at BaseClass.<computed> [as publish] (/usr/src/app/node_modules/@redis/client/dist/lib/commander.js:8:29)\n    at RedisAdapter.broadcast (/usr/src/app/node_modules/@socket.io/redis-adapter/dist/index.js:473:28)\n    at BroadcastOperator.emit (/usr/src/app/node_modules/socket.io/dist/broadcast-operator.js:169:26)\n    at updateUserStatus (/usr/src/app/src/socket/socketService.js:212:44)","rejection":true,"date":"Mon Jun 16 2025 18:07:14 GMT+0000 (Coordinated Universal Time)","process":{"pid":2890,"uid":0,"gid":0,"cwd":"/usr/src/app","execPath":"/usr/local/bin/node","version":"v20.17.0","argv":["/usr/local/bin/node","/usr/src/app/src/server.js","src/server.js"],"memoryUsage":{"rss":138739712,"heapTotal":46280704,"heapUsed":43504088,"external":3413061,"arrayBuffers":138988}},"os":{"loadavg":[1.53,1.88,1.85],"uptime":18162.75},"trace":[{"column":31,"file":"/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js","function":"Commander._RedisClient_sendCommand","line":520,"method":"_RedisClient_sendCommand","native":false},{"column":154,"file":"/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js","function":"Commander.commandsExecutor","line":190,"method":"commandsExecutor","native":false},{"column":29,"file":"/usr/src/app/node_modules/@redis/client/dist/lib/commander.js","function":"BaseClass.<computed> [as publish]","line":8,"method":"<computed> [as publish]","native":false},{"column":28,"file":"/usr/src/app/node_modules/@socket.io/redis-adapter/dist/index.js","function":"RedisAdapter.broadcast","line":473,"method":"broadcast","native":false},{"column":26,"file":"/usr/src/app/node_modules/socket.io/dist/broadcast-operator.js","function":"BroadcastOperator.emit","line":169,"method":"emit","native":false},{"column":44,"file":"/usr/src/app/src/socket/socketService.js","function":"updateUserStatus","line":212,"method":null,"native":false}]}
{"timestamp":"2025-06-16 18:18:20","level":"error","message":"unhandledRejection: The client is closed\nError: The client is closed\n    at Commander._RedisClient_sendCommand (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:520:31)\n    at Commander.commandsExecutor (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:190:154)\n    at BaseClass.<computed> [as publish] (/usr/src/app/node_modules/@redis/client/dist/lib/commander.js:8:29)\n    at RedisAdapter.broadcast (/usr/src/app/node_modules/@socket.io/redis-adapter/dist/index.js:473:28)\n    at BroadcastOperator.emit (/usr/src/app/node_modules/socket.io/dist/broadcast-operator.js:169:26)\n    at updateUserStatus (/usr/src/app/src/socket/socketService.js:212:44)","error":{},"stack":"Error: The client is closed\n    at Commander._RedisClient_sendCommand (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:520:31)\n    at Commander.commandsExecutor (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:190:154)\n    at BaseClass.<computed> [as publish] (/usr/src/app/node_modules/@redis/client/dist/lib/commander.js:8:29)\n    at RedisAdapter.broadcast (/usr/src/app/node_modules/@socket.io/redis-adapter/dist/index.js:473:28)\n    at BroadcastOperator.emit (/usr/src/app/node_modules/socket.io/dist/broadcast-operator.js:169:26)\n    at updateUserStatus (/usr/src/app/src/socket/socketService.js:212:44)","rejection":true,"date":"Mon Jun 16 2025 18:18:20 GMT+0000 (Coordinated Universal Time)","process":{"pid":128,"uid":0,"gid":0,"cwd":"/usr/src/app","execPath":"/usr/local/bin/node","version":"v20.17.0","argv":["/usr/local/bin/node","/usr/src/app/src/server.js","src/server.js"],"memoryUsage":{"rss":138276864,"heapTotal":45756416,"heapUsed":43452776,"external":3405118,"arrayBuffers":130427}},"os":{"loadavg":[1.64,1.41,1.63],"uptime":18828.58},"trace":[{"column":31,"file":"/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js","function":"Commander._RedisClient_sendCommand","line":520,"method":"_RedisClient_sendCommand","native":false},{"column":154,"file":"/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js","function":"Commander.commandsExecutor","line":190,"method":"commandsExecutor","native":false},{"column":29,"file":"/usr/src/app/node_modules/@redis/client/dist/lib/commander.js","function":"BaseClass.<computed> [as publish]","line":8,"method":"<computed> [as publish]","native":false},{"column":28,"file":"/usr/src/app/node_modules/@socket.io/redis-adapter/dist/index.js","function":"RedisAdapter.broadcast","line":473,"method":"broadcast","native":false},{"column":26,"file":"/usr/src/app/node_modules/socket.io/dist/broadcast-operator.js","function":"BroadcastOperator.emit","line":169,"method":"emit","native":false},{"column":44,"file":"/usr/src/app/src/socket/socketService.js","function":"updateUserStatus","line":212,"method":null,"native":false}]}
{"timestamp":"2025-06-16 18:24:12","level":"error","message":"unhandledRejection: The client is closed\nError: The client is closed\n    at Commander._RedisClient_sendCommand (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:520:31)\n    at Commander.commandsExecutor (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:190:154)\n    at BaseClass.<computed> [as publish] (/usr/src/app/node_modules/@redis/client/dist/lib/commander.js:8:29)\n    at RedisAdapter.broadcast (/usr/src/app/node_modules/@socket.io/redis-adapter/dist/index.js:473:28)\n    at BroadcastOperator.emit (/usr/src/app/node_modules/socket.io/dist/broadcast-operator.js:169:26)\n    at updateUserStatus (/usr/src/app/src/socket/socketService.js:212:44)","error":{},"stack":"Error: The client is closed\n    at Commander._RedisClient_sendCommand (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:520:31)\n    at Commander.commandsExecutor (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:190:154)\n    at BaseClass.<computed> [as publish] (/usr/src/app/node_modules/@redis/client/dist/lib/commander.js:8:29)\n    at RedisAdapter.broadcast (/usr/src/app/node_modules/@socket.io/redis-adapter/dist/index.js:473:28)\n    at BroadcastOperator.emit (/usr/src/app/node_modules/socket.io/dist/broadcast-operator.js:169:26)\n    at updateUserStatus (/usr/src/app/src/socket/socketService.js:212:44)","rejection":true,"date":"Mon Jun 16 2025 18:24:12 GMT+0000 (Coordinated Universal Time)","process":{"pid":128,"uid":0,"gid":0,"cwd":"/usr/src/app","execPath":"/usr/local/bin/node","version":"v20.17.0","argv":["/usr/local/bin/node","/usr/src/app/src/server.js","src/server.js"],"memoryUsage":{"rss":197505024,"heapTotal":108146688,"heapUsed":42175456,"external":3383681,"arrayBuffers":109608}},"os":{"loadavg":[1.04,1.24,1.48],"uptime":19180.39},"trace":[{"column":31,"file":"/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js","function":"Commander._RedisClient_sendCommand","line":520,"method":"_RedisClient_sendCommand","native":false},{"column":154,"file":"/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js","function":"Commander.commandsExecutor","line":190,"method":"commandsExecutor","native":false},{"column":29,"file":"/usr/src/app/node_modules/@redis/client/dist/lib/commander.js","function":"BaseClass.<computed> [as publish]","line":8,"method":"<computed> [as publish]","native":false},{"column":28,"file":"/usr/src/app/node_modules/@socket.io/redis-adapter/dist/index.js","function":"RedisAdapter.broadcast","line":473,"method":"broadcast","native":false},{"column":26,"file":"/usr/src/app/node_modules/socket.io/dist/broadcast-operator.js","function":"BroadcastOperator.emit","line":169,"method":"emit","native":false},{"column":44,"file":"/usr/src/app/src/socket/socketService.js","function":"updateUserStatus","line":212,"method":null,"native":false}]}
{"timestamp":"2025-06-16 18:28:45","level":"error","message":"unhandledRejection: The client is closed\nError: The client is closed\n    at RedisSocket.quit (/usr/src/app/node_modules/@redis/client/dist/lib/client/socket.js:70:19)\n    at Commander.QUIT (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:260:71)\n    at CacheService.close (/usr/src/app/src/services/cacheService.js:70:25)\n    at process.<anonymous> (/usr/src/app/src/server.js:305:22)\n    at process.emit (node:events:519:28)","error":{},"stack":"Error: The client is closed\n    at RedisSocket.quit (/usr/src/app/node_modules/@redis/client/dist/lib/client/socket.js:70:19)\n    at Commander.QUIT (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:260:71)\n    at CacheService.close (/usr/src/app/src/services/cacheService.js:70:25)\n    at process.<anonymous> (/usr/src/app/src/server.js:305:22)\n    at process.emit (node:events:519:28)","rejection":true,"date":"Mon Jun 16 2025 18:28:45 GMT+0000 (Coordinated Universal Time)","process":{"pid":338,"uid":0,"gid":0,"cwd":"/usr/src/app","execPath":"/usr/local/bin/node","version":"v20.17.0","argv":["/usr/local/bin/node","/usr/src/app/src/server.js","src/server.js"],"memoryUsage":{"rss":129818624,"heapTotal":41299968,"heapUsed":39398152,"external":3368700,"arrayBuffers":96675}},"os":{"loadavg":[2.62,1.67,1.58],"uptime":19454.27},"trace":[{"column":19,"file":"/usr/src/app/node_modules/@redis/client/dist/lib/client/socket.js","function":"RedisSocket.quit","line":70,"method":"quit","native":false},{"column":71,"file":"/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js","function":"Commander.QUIT","line":260,"method":"QUIT","native":false},{"column":25,"file":"/usr/src/app/src/services/cacheService.js","function":"CacheService.close","line":70,"method":"close","native":false},{"column":22,"file":"/usr/src/app/src/server.js","function":null,"line":305,"method":null,"native":false},{"column":28,"file":"node:events","function":"process.emit","line":519,"method":"emit","native":false}]}
{"timestamp":"2025-06-16 18:38:52","level":"error","message":"unhandledRejection: The client is closed\nError: The client is closed\n    at RedisSocket.quit (/usr/src/app/node_modules/@redis/client/dist/lib/client/socket.js:70:19)\n    at Commander.QUIT (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:260:71)\n    at CacheService.close (/usr/src/app/src/services/cacheService.js:70:25)\n    at process.<anonymous> (/usr/src/app/src/server.js:305:22)\n    at process.emit (node:events:519:28)","error":{},"stack":"Error: The client is closed\n    at RedisSocket.quit (/usr/src/app/node_modules/@redis/client/dist/lib/client/socket.js:70:19)\n    at Commander.QUIT (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:260:71)\n    at CacheService.close (/usr/src/app/src/services/cacheService.js:70:25)\n    at process.<anonymous> (/usr/src/app/src/server.js:305:22)\n    at process.emit (node:events:519:28)","rejection":true,"date":"Mon Jun 16 2025 18:38:52 GMT+0000 (Coordinated Universal Time)","process":{"pid":128,"uid":0,"gid":0,"cwd":"/usr/src/app","execPath":"/usr/local/bin/node","version":"v20.17.0","argv":["/usr/local/bin/node","/usr/src/app/src/server.js","src/server.js"],"memoryUsage":{"rss":135827456,"heapTotal":42610688,"heapUsed":40864792,"external":3371016,"arrayBuffers":96675}},"os":{"loadavg":[2.58,2.06,1.79],"uptime":20060.58},"trace":[{"column":19,"file":"/usr/src/app/node_modules/@redis/client/dist/lib/client/socket.js","function":"RedisSocket.quit","line":70,"method":"quit","native":false},{"column":71,"file":"/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js","function":"Commander.QUIT","line":260,"method":"QUIT","native":false},{"column":25,"file":"/usr/src/app/src/services/cacheService.js","function":"CacheService.close","line":70,"method":"close","native":false},{"column":22,"file":"/usr/src/app/src/server.js","function":null,"line":305,"method":null,"native":false},{"column":28,"file":"node:events","function":"process.emit","line":519,"method":"emit","native":false}]}
{"timestamp":"2025-06-16 18:51:16","level":"error","message":"unhandledRejection: The client is closed\nError: The client is closed\n    at RedisSocket.quit (/usr/src/app/node_modules/@redis/client/dist/lib/client/socket.js:70:19)\n    at Commander.QUIT (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:260:71)\n    at CacheService.close (/usr/src/app/src/services/cacheService.js:70:25)\n    at process.<anonymous> (/usr/src/app/src/server.js:305:22)\n    at process.emit (node:events:519:28)","error":{},"stack":"Error: The client is closed\n    at RedisSocket.quit (/usr/src/app/node_modules/@redis/client/dist/lib/client/socket.js:70:19)\n    at Commander.QUIT (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:260:71)\n    at CacheService.close (/usr/src/app/src/services/cacheService.js:70:25)\n    at process.<anonymous> (/usr/src/app/src/server.js:305:22)\n    at process.emit (node:events:519:28)","rejection":true,"date":"Mon Jun 16 2025 18:51:16 GMT+0000 (Coordinated Universal Time)","process":{"pid":338,"uid":0,"gid":0,"cwd":"/usr/src/app","execPath":"/usr/local/bin/node","version":"v20.17.0","argv":["/usr/local/bin/node","/usr/src/app/src/server.js","src/server.js"],"memoryUsage":{"rss":136851456,"heapTotal":43921408,"heapUsed":42417888,"external":3407494,"arrayBuffers":113066}},"os":{"loadavg":[1.24,1.35,1.42],"uptime":20804.43},"trace":[{"column":19,"file":"/usr/src/app/node_modules/@redis/client/dist/lib/client/socket.js","function":"RedisSocket.quit","line":70,"method":"quit","native":false},{"column":71,"file":"/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js","function":"Commander.QUIT","line":260,"method":"QUIT","native":false},{"column":25,"file":"/usr/src/app/src/services/cacheService.js","function":"CacheService.close","line":70,"method":"close","native":false},{"column":22,"file":"/usr/src/app/src/server.js","function":null,"line":305,"method":null,"native":false},{"column":28,"file":"node:events","function":"process.emit","line":519,"method":"emit","native":false}]}
{"timestamp":"2025-06-16 18:55:09","level":"error","message":"unhandledRejection: The client is closed\nError: The client is closed\n    at Commander._RedisClient_sendCommand (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:520:31)\n    at Commander.commandsExecutor (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:190:154)\n    at BaseClass.<computed> [as publish] (/usr/src/app/node_modules/@redis/client/dist/lib/commander.js:8:29)\n    at RedisAdapter.broadcast (/usr/src/app/node_modules/@socket.io/redis-adapter/dist/index.js:473:28)\n    at BroadcastOperator.emit (/usr/src/app/node_modules/socket.io/dist/broadcast-operator.js:169:26)\n    at updateUserStatus (/usr/src/app/src/socket/socketService.js:212:44)","error":{},"stack":"Error: The client is closed\n    at Commander._RedisClient_sendCommand (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:520:31)\n    at Commander.commandsExecutor (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:190:154)\n    at BaseClass.<computed> [as publish] (/usr/src/app/node_modules/@redis/client/dist/lib/commander.js:8:29)\n    at RedisAdapter.broadcast (/usr/src/app/node_modules/@socket.io/redis-adapter/dist/index.js:473:28)\n    at BroadcastOperator.emit (/usr/src/app/node_modules/socket.io/dist/broadcast-operator.js:169:26)\n    at updateUserStatus (/usr/src/app/src/socket/socketService.js:212:44)","rejection":true,"date":"Mon Jun 16 2025 18:55:09 GMT+0000 (Coordinated Universal Time)","process":{"pid":139,"uid":0,"gid":0,"cwd":"/usr/src/app","execPath":"/usr/local/bin/node","version":"v20.17.0","argv":["/usr/local/bin/node","/usr/src/app/src/server.js","src/server.js"],"memoryUsage":{"rss":136269824,"heapTotal":45232128,"heapUsed":42971472,"external":3408543,"arrayBuffers":133840}},"os":{"loadavg":[1.55,1.44,1.44],"uptime":21037.46},"trace":[{"column":31,"file":"/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js","function":"Commander._RedisClient_sendCommand","line":520,"method":"_RedisClient_sendCommand","native":false},{"column":154,"file":"/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js","function":"Commander.commandsExecutor","line":190,"method":"commandsExecutor","native":false},{"column":29,"file":"/usr/src/app/node_modules/@redis/client/dist/lib/commander.js","function":"BaseClass.<computed> [as publish]","line":8,"method":"<computed> [as publish]","native":false},{"column":28,"file":"/usr/src/app/node_modules/@socket.io/redis-adapter/dist/index.js","function":"RedisAdapter.broadcast","line":473,"method":"broadcast","native":false},{"column":26,"file":"/usr/src/app/node_modules/socket.io/dist/broadcast-operator.js","function":"BroadcastOperator.emit","line":169,"method":"emit","native":false},{"column":44,"file":"/usr/src/app/src/socket/socketService.js","function":"updateUserStatus","line":212,"method":null,"native":false}]}
