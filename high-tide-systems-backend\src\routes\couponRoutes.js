const express = require('express');
const router = express.Router();
const { body } = require('express-validator');
const CouponController = require('../controllers/couponController');
const { authenticate } = require('../middlewares/auth');

// Criar cupom (admin)
router.post('/create', authenticate, body('code').notEmpty(), CouponController.create);
// Validar cupom (público)
router.post('/validate', body('code').notEmpty(), CouponController.validate);
// Resgatar cupom (usuário logado)
router.post('/redeem', authenticate, body('code').notEmpty(), CouponController.redeem);

module.exports = router;
