// src/services/validationService.js
const Joi = require('joi');
const loggerService = require('./loggerService');

/**
 * Serviço centralizado de validação usando Joi
 * Fornece schemas reutilizáveis e validação consistente
 */
class ValidationService {
  constructor() {
    this.schemas = {};
    this.initializeCommonSchemas();
  }

  /**
   * Inicializar schemas comuns reutilizáveis
   */
  initializeCommonSchemas() {
    // Schemas básicos
    this.schemas.uuid = Joi.string().uuid().required();
    this.schemas.optionalUuid = Joi.string().uuid().optional();
    this.schemas.email = Joi.string().email().required();
    this.schemas.optionalEmail = Joi.string().email().optional().allow('');
    this.schemas.phone = Joi.string().pattern(/^\d{10,11}$/).optional().allow('');
    this.schemas.cpf = Joi.string().pattern(/^\d{11}$/).optional().allow('');
    this.schemas.cnpj = Joi.string().pattern(/^\d{14}$/).optional().allow('');
    this.schemas.password = Joi.string().min(6).required();
    this.schemas.name = Joi.string().min(2).max(100).required();
    this.schemas.optionalName = Joi.string().min(2).max(100).optional().allow('');
    this.schemas.date = Joi.date().iso().optional();
    this.schemas.boolean = Joi.boolean().optional();
    this.schemas.pagination = Joi.object({
      page: Joi.number().integer().min(1).default(1),
      limit: Joi.number().integer().min(1).max(10000).default(10)
    });

    // Schema para User
    this.schemas.user = {
      create: Joi.object({
        fullName: this.schemas.name,
        email: this.schemas.email,
        login: Joi.string().min(3).max(50).required(),
        password: this.schemas.password,
        phone: this.schemas.phone,
        cpf: this.schemas.cpf,
        cnpj: this.schemas.cnpj,
        address: this.schemas.optionalName,
        city: this.schemas.optionalName,
        state: Joi.string().length(2).optional().allow(''),
        postalCode: Joi.string().pattern(/^\d{8}$/).optional().allow(''),
        birthDate: this.schemas.date,
        role: Joi.string().valid('SYSTEM_ADMIN', 'COMPANY_ADMIN', 'EMPLOYEE').default('EMPLOYEE'),
        modules: Joi.array().items(Joi.string().valid('ADMIN', 'RH', 'FINANCIAL', 'SCHEDULING', 'BASIC')).default(['BASIC']),
        permissions: Joi.array().items(Joi.string()).default([]),
        professionId: this.schemas.optionalUuid,
        branchId: this.schemas.optionalUuid,
        companyId: this.schemas.optionalUuid
      }),
      update: Joi.object({
        fullName: this.schemas.optionalName,
        email: this.schemas.optionalEmail,
        phone: this.schemas.phone,
        cpf: this.schemas.cpf,
        cnpj: this.schemas.cnpj,
        address: this.schemas.optionalName,
        city: this.schemas.optionalName,
        state: Joi.string().length(2).optional().allow(''),
        postalCode: Joi.string().pattern(/^\d{8}$/).optional().allow(''),
        birthDate: this.schemas.date,
        modules: Joi.array().items(Joi.string().valid('ADMIN', 'RH', 'FINANCIAL', 'SCHEDULING', 'BASIC')),
        permissions: Joi.array().items(Joi.string()),
        professionId: this.schemas.optionalUuid,
        branchId: this.schemas.optionalUuid,
        active: this.schemas.boolean
      })
    };

    // Schema para Person
    this.schemas.person = {
      create: Joi.object({
        fullName: this.schemas.name,
        email: this.schemas.optionalEmail,
        phone: this.schemas.phone,
        cpf: this.schemas.cpf,
        address: this.schemas.optionalName,
        city: this.schemas.optionalName,
        state: Joi.string().length(2).optional().allow(''),
        postalCode: Joi.string().pattern(/^\d{8}$/).optional().allow(''),
        neighborhood: this.schemas.optionalName,
        birthDate: this.schemas.date,
        gender: Joi.string().valid('M', 'F', 'O').optional().allow(''),
        relationship: this.schemas.optionalName,
        notes: Joi.string().max(1000).optional().allow(''),
        useClientEmail: this.schemas.boolean,
        useClientPhone: this.schemas.boolean
      }),
      update: Joi.object({
        fullName: this.schemas.optionalName,
        email: this.schemas.optionalEmail,
        phone: this.schemas.phone,
        cpf: this.schemas.cpf,
        address: this.schemas.optionalName,
        city: this.schemas.optionalName,
        state: Joi.string().length(2).optional().allow(''),
        postalCode: Joi.string().pattern(/^\d{8}$/).optional().allow(''),
        neighborhood: this.schemas.optionalName,
        birthDate: this.schemas.date,
        gender: Joi.string().valid('M', 'F', 'O').optional().allow(''),
        relationship: this.schemas.optionalName,
        notes: Joi.string().max(1000).optional().allow(''),
        useClientEmail: this.schemas.boolean,
        useClientPhone: this.schemas.boolean,
        active: this.schemas.boolean
      })
    };

    // Schema para Company
    this.schemas.company = {
      create: Joi.object({
        name: this.schemas.name,
        tradingName: this.schemas.optionalName,
        legalName: this.schemas.optionalName,
        cnpj: this.schemas.cnpj,
        phone: this.schemas.phone,
        phone2: this.schemas.phone,
        contactEmail: this.schemas.optionalEmail,
        address: this.schemas.optionalName,
        city: this.schemas.optionalName,
        state: Joi.string().length(2).optional().allow(''),
        postalCode: Joi.string().pattern(/^\d{8}$/).optional().allow(''),
        website: Joi.string().uri().optional().allow(''),
        industry: this.schemas.optionalName,
        description: Joi.string().max(500).optional().allow(''),
        primaryColor: Joi.string().pattern(/^#[0-9A-F]{6}$/i).optional().allow(''),
        secondaryColor: Joi.string().pattern(/^#[0-9A-F]{6}$/i).optional().allow(''),
        defaultCurrency: Joi.string().length(3).default('BRL'),
        timeZone: Joi.string().default('America/Sao_Paulo')
      }),
      update: Joi.object({
        name: this.schemas.optionalName,
        tradingName: this.schemas.optionalName,
        legalName: this.schemas.optionalName,
        phone: this.schemas.phone,
        phone2: this.schemas.phone,
        contactEmail: this.schemas.optionalEmail,
        address: this.schemas.optionalName,
        city: this.schemas.optionalName,
        state: Joi.string().length(2).optional().allow(''),
        postalCode: Joi.string().pattern(/^\d{8}$/).optional().allow(''),
        website: Joi.string().uri().optional().allow(''),
        industry: this.schemas.optionalName,
        description: Joi.string().max(500).optional().allow(''),
        primaryColor: Joi.string().pattern(/^#[0-9A-F]{6}$/i).optional().allow(''),
        secondaryColor: Joi.string().pattern(/^#[0-9A-F]{6}$/i).optional().allow(''),
        defaultCurrency: Joi.string().length(3),
        timeZone: Joi.string(),
        active: this.schemas.boolean
      })
    };

    // Schema para Scheduling
    this.schemas.scheduling = {
      create: Joi.object({
        title: this.schemas.name,
        description: Joi.string().max(500).optional().allow(''),
        startDate: Joi.date().iso().required(),
        endDate: Joi.date().iso().greater(Joi.ref('startDate')).required(),
        userId: this.schemas.uuid,
        clientId: this.schemas.uuid,
        locationId: this.schemas.uuid,
        serviceTypeId: this.schemas.uuid,
        insuranceId: this.schemas.optionalUuid,
        personIds: Joi.array().items(this.schemas.uuid).min(1).required(),
        status: Joi.string().valid('PENDING', 'CONFIRMED', 'CANCELLED', 'COMPLETED', 'NO_SHOW').default('PENDING')
      }),
      update: Joi.object({
        title: this.schemas.optionalName,
        description: Joi.string().max(500).optional().allow(''),
        startDate: Joi.date().iso(),
        endDate: Joi.date().iso(),
        userId: this.schemas.optionalUuid,
        locationId: this.schemas.optionalUuid,
        serviceTypeId: this.schemas.optionalUuid,
        insuranceId: this.schemas.optionalUuid,
        personIds: Joi.array().items(this.schemas.uuid).min(1),
        status: Joi.string().valid('PENDING', 'CONFIRMED', 'CANCELLED', 'COMPLETED', 'NO_SHOW')
      }).custom((value, helpers) => {
        // Validar que endDate é maior que startDate se ambos estão presentes
        if (value.startDate && value.endDate && value.endDate <= value.startDate) {
          return helpers.error('custom.endDateMustBeAfterStartDate');
        }
        return value;
      })
    };

    // Schema para Login
    this.schemas.auth = {
      login: Joi.object({
        email: Joi.string().required(),
        password: Joi.string().required(),
        loginType: Joi.string().valid('email', 'username').default('email')
      }),
      changePassword: Joi.object({
        currentPassword: Joi.string().required(),
        newPassword: this.schemas.password,
        confirmPassword: Joi.string().valid(Joi.ref('newPassword')).required()
      }),
      resetPassword: Joi.object({
        token: Joi.string().required(),
        newPassword: this.schemas.password,
        confirmPassword: Joi.string().valid(Joi.ref('newPassword')).required()
      })
    };
  }

  /**
   * Validar dados usando um schema específico
   */
  async validate(data, schemaPath, options = {}) {
    try {
      const schema = this.getSchema(schemaPath);
      if (!schema) {
        throw new Error(`Schema não encontrado: ${schemaPath}`);
      }

      const validationOptions = {
        abortEarly: false,
        stripUnknown: true,
        ...options
      };

      const { error, value } = schema.validate(data, validationOptions);

      if (error) {
        const validationErrors = error.details.map(detail => ({
          field: detail.path.join('.'),
          message: detail.message,
          value: detail.context?.value
        }));

        loggerService.warn('VALIDATION_ERROR', {
          schemaPath,
          errors: validationErrors,
          originalData: data
        });

        return {
          isValid: false,
          errors: validationErrors,
          data: null
        };
      }

      return {
        isValid: true,
        errors: null,
        data: value
      };
    } catch (error) {
      loggerService.error('VALIDATION_SERVICE_ERROR', {
        error: error.message,
        schemaPath,
        data
      });

      return {
        isValid: false,
        errors: [{ field: 'general', message: 'Erro interno de validação' }],
        data: null
      };
    }
  }

  /**
   * Obter schema por caminho (ex: 'user.create', 'person.update')
   */
  getSchema(schemaPath) {
    const parts = schemaPath.split('.');
    let schema = this.schemas;

    for (const part of parts) {
      schema = schema[part];
      if (!schema) {
        return null;
      }
    }

    return schema;
  }

  /**
   * Adicionar schema customizado
   */
  addSchema(path, schema) {
    const parts = path.split('.');
    let current = this.schemas;

    for (let i = 0; i < parts.length - 1; i++) {
      const part = parts[i];
      if (!current[part]) {
        current[part] = {};
      }
      current = current[part];
    }

    current[parts[parts.length - 1]] = schema;
  }

  /**
   * Middleware Express para validação
   */
  middleware(schemaPath, options = {}) {
    return async (req, res, next) => {
      const dataToValidate = options.validateQuery ? req.query : req.body;
      
      const result = await this.validate(dataToValidate, schemaPath, options);

      if (!result.isValid) {
        return res.status(400).json({
          message: 'Dados inválidos',
          errors: result.errors
        });
      }

      // Substituir dados originais pelos dados validados e sanitizados
      if (options.validateQuery) {
        req.query = result.data;
      } else {
        req.body = result.data;
      }

      next();
    };
  }
}

// Criar instância singleton
const validationService = new ValidationService();

module.exports = validationService;
