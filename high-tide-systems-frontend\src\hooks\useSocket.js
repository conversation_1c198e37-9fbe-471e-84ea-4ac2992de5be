'use client';

import { useEffect, useState } from 'react';
import io from 'socket.io-client';

// Função auxiliar para obter o token atual
const getCurrentToken = () => {
  if (typeof window !== 'undefined') {
    return localStorage.getItem('token');
  }
  return null;
};

export function useSocket(url) {
  const [socket, setSocket] = useState(null);

  useEffect(() => {
    // Verificar se estamos no lado do cliente
    if (typeof window === 'undefined') return;

    // Verificar se o usuário está autenticado
    const token = getCurrentToken();
    if (!token) return;

    // Criar instância do socket
    const socketInstance = io(url, {
      auth: {
        token
      },
      reconnectionAttempts: 5,
      reconnectionDelay: 1000,
      autoConnect: true
    });

    // Configurar handlers de conexão
    socketInstance.on('connect', () => {
      console.log('[SOCKET] Conectado:', socketInstance.id);
    });

    socketInstance.on('connect_error', (err) => {
      console.error('[SOCKET] Erro de conexão:', err.message);
    });

    socketInstance.on('disconnect', (reason) => {
      console.log('[SOCKET] Desconectado:', reason);
    });

    setSocket(socketInstance);

    // Limpar socket ao desmontar
    return () => {
      if (socketInstance) {
        socketInstance.disconnect();
      }
    };
  }, [url]);

  return socket;
}
