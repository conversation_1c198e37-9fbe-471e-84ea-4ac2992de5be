// src/controllers/couponController.js
const prisma = require('../utils/prisma');

class CouponController {
  // Criar cupom (admin)
  static async create(req, res) {
    try {
      const { code, description, type, value, maxRedemptions, expiresAt } = req.body;
      const coupon = await prisma.coupon.create({
        data: {
          code: code.toUpperCase(),
          description,
          type,
          value,
          maxRedemptions,
          expiresAt: expiresAt ? new Date(expiresAt) : null,
        },
      });
      res.status(201).json(coupon);
    } catch (error) {
      console.error('Erro ao criar cupom:', error);
      res.status(500).json({ message: 'Erro ao criar cupom.' });
    }
  }

  // Validar cupom (público)
  static async validate(req, res) {
    try {
      const { code } = req.body;
      const coupon = await prisma.coupon.findUnique({ where: { code: code.toUpperCase() } });
      if (!coupon || !coupon.active) return res.status(404).json({ message: 'Cupom inválido.' });
      if (coupon.expiresAt && coupon.expiresAt < new Date()) return res.status(400).json({ message: 'Cupom expirado.' });
      if (coupon.maxRedemptions) {
        const count = await prisma.couponRedemption.count({ where: { couponId: coupon.id } });
        if (count >= coupon.maxRedemptions) return res.status(400).json({ message: 'Cupom esgotado.' });
      }
      res.json({ valid: true, coupon });
    } catch (error) {
      console.error('Erro ao validar cupom:', error);
      res.status(500).json({ message: 'Erro ao validar cupom.' });
    }
  }

  // Resgatar cupom (aplica para empresa/usuário)
  static async redeem(req, res) {
    try {
      const { code } = req.body;
      const userId = req.user?.id;
      const companyId = req.user?.companyId;
      const coupon = await prisma.coupon.findUnique({ where: { code: code.toUpperCase() } });
      if (!coupon || !coupon.active) return res.status(404).json({ message: 'Cupom inválido.' });
      if (coupon.expiresAt && coupon.expiresAt < new Date()) return res.status(400).json({ message: 'Cupom expirado.' });
      if (coupon.maxRedemptions) {
        const count = await prisma.couponRedemption.count({ where: { couponId: coupon.id } });
        if (count >= coupon.maxRedemptions) return res.status(400).json({ message: 'Cupom esgotado.' });
      }
      // Verifica se já foi resgatado por este usuário/empresa
      const already = await prisma.couponRedemption.findFirst({ where: { couponId: coupon.id, companyId } });
      if (already) return res.status(400).json({ message: 'Cupom já utilizado.' });
      // Aplica benefício (exemplo: trial extra)
      if (coupon.type === 'TRIAL_EXTENSION') {
        await prisma.company.update({
          where: { id: companyId },
          data: { trialEnd: { increment: Math.round(coupon.value * 24 * 60 * 60 * 1000) } }
        });
      }
      // Salva resgate
      await prisma.couponRedemption.create({ data: { couponId: coupon.id, userId, companyId } });
      res.json({ message: 'Cupom resgatado com sucesso!', coupon });
    } catch (error) {
      console.error('Erro ao resgatar cupom:', error);
      res.status(500).json({ message: 'Erro ao resgatar cupom.' });
    }
  }
}

module.exports = CouponController;
