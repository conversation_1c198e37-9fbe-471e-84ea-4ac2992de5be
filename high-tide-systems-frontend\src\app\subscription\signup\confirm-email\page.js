"use client";

import { useState, useEffect } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { Mail, CheckCircle, AlertTriangle, ArrowLeft, RotateCcw } from "lucide-react";
import { api } from "@/utils/api";
import { useAuth } from "@/contexts/AuthContext";
import Link from "next/link";

export default function ConfirmEmailPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { login } = useAuth();
  
  const [email, setEmail] = useState("");
  const [code, setCode] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [isResending, setIsResending] = useState(false);
  const [error, setError] = useState("");
  const [success, setSuccess] = useState("");
  const [countdown, setCountdown] = useState(0);

  useEffect(() => {
    const emailParam = searchParams.get("email");
    if (emailParam) {
      setEmail(emailParam);
    }
  }, [searchParams]);

  // Countdown para reenvio
  useEffect(() => {
    let timer;
    if (countdown > 0) {
      timer = setTimeout(() => setCountdown(countdown - 1), 1000);
    }
    return () => clearTimeout(timer);
  }, [countdown]);

  const handleCodeChange = (e) => {
    const value = e.target.value.replace(/\D/g, "").slice(0, 6);
    setCode(value);
    setError("");
  };

  const handleVerifyCode = async () => {
    if (!code || code.length !== 6) {
      setError("Digite o código de 6 dígitos");
      return;
    }

    setIsLoading(true);
    setError("");

    try {
      // 1. Verificar o código de email
      await api.post("/auth/verify-email-confirmation", {
        email,
        code
      });

      // 2. Recuperar dados salvos do localStorage
      const pendingSignupData = localStorage.getItem('pendingSignup');
      if (!pendingSignupData) {
        setError("Dados do cadastro não encontrados. Tente fazer o cadastro novamente.");
        return;
      }

      const signupData = JSON.parse(pendingSignupData);

      // 3. Criar a conta do usuário
      await api.post("/auth/register", signupData);
      
      // 4. Limpar dados temporários
      localStorage.removeItem('pendingSignup');

      // 5. Fazer login automático usando o contexto de autenticação
      // Isso garante que o estado seja atualizado corretamente
      setSuccess("Conta criada! Fazendo login...");
      
      await login(signupData.email, signupData.password, 'email');
      
      // O login() do contexto já redireciona para o dashboard automaticamente

    } catch (error) {
      console.error("Erro ao verificar código:", error);
      setError(error.response?.data?.message || "Erro ao verificar código. Tente novamente.");
    } finally {
      setIsLoading(false);
    }
  };

  const handleResendCode = async () => {
    if (countdown > 0) return;

    setIsResending(true);
    setError("");

    try {
      // Tentar obter o nome do usuário dos dados salvos
      const pendingSignupData = localStorage.getItem('pendingSignup');
      let userName = null;
      
      if (pendingSignupData) {
        const signupData = JSON.parse(pendingSignupData);
        userName = signupData.fullName;
      }

      await api.post("/auth/request-email-confirmation", { 
        email,
        userName // ✅ Incluir nome para email personalizado
      });
      
      setSuccess("Novo código enviado para seu email!");
      setCountdown(60); // 60 segundos de cooldown
    } catch (error) {
      setError("Erro ao reenviar código. Tente novamente.");
    } finally {
      setIsResending(false);
    }
  };

  const handleKeyPress = (e) => {
    if (e.key === 'Enter') {
      handleVerifyCode();
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center p-4">
      <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-xl w-full max-w-md p-8">
        {/* Header */}
        <div className="text-center mb-8">
          <div className="flex justify-center mb-4">
            <div className="bg-blue-100 dark:bg-blue-900/30 p-4 rounded-full">
              <Mail className="h-8 w-8 text-blue-600 dark:text-blue-400" />
            </div>
          </div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
            Confirme seu email
          </h1>
          <p className="text-sm text-gray-600 dark:text-gray-400">
            Enviamos um código de 6 dígitos para
          </p>
          <p className="text-sm font-medium text-blue-600 dark:text-blue-400 mt-1">
            {email}
          </p>
        </div>

        {/* Messages */}
        {error && (
          <div className="mb-6 p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg flex items-center">
            <AlertTriangle className="h-5 w-5 text-red-500 mr-2" />
            <span className="text-sm text-red-600 dark:text-red-400">{error}</span>
          </div>
        )}

        {success && (
          <div className="mb-6 p-4 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg flex items-center">
            <CheckCircle className="h-5 w-5 text-green-500 mr-2" />
            <span className="text-sm text-green-600 dark:text-green-400">{success}</span>
          </div>
        )}

        {/* Code Input */}
        <div className="mb-6">
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Código de verificação
          </label>
          <input
            type="text"
            value={code}
            onChange={handleCodeChange}
            onKeyPress={handleKeyPress}
            placeholder="000000"
            className="w-full px-4 py-3 text-center text-2xl font-mono tracking-widest border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-gray-900 dark:text-white bg-white dark:bg-gray-700"
            maxLength={6}
            disabled={isLoading}
            autoFocus
          />
          <p className="text-xs text-gray-500 dark:text-gray-400 mt-1 text-center">
            Digite o código de 6 dígitos enviado para seu email
          </p>
        </div>

        {/* Verify Button */}
        <button
          onClick={handleVerifyCode}
          disabled={isLoading || code.length !== 6}
          className="w-full py-3 px-4 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white font-medium rounded-lg transition-colors disabled:cursor-not-allowed flex items-center justify-center"
        >
          {isLoading ? (
            <>
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
              Verificando...
            </>
          ) : (
            <>
              <CheckCircle className="h-4 w-4 mr-2" />
              Confirmar Email
            </>
          )}
        </button>

        {/* Resend Code */}
        <div className="mt-6 text-center">
          <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">
            Não recebeu o código?
          </p>
          <button
            onClick={handleResendCode}
            disabled={countdown > 0 || isResending}
            className="text-sm text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 disabled:text-gray-400 disabled:cursor-not-allowed flex items-center justify-center mx-auto"
          >
            {isResending ? (
              <>
                <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-blue-600 mr-1"></div>
                Reenviando...
              </>
            ) : countdown > 0 ? (
              <>
                <RotateCcw className="h-3 w-3 mr-1" />
                Reenviar em {countdown}s
              </>
            ) : (
              <>
                <RotateCcw className="h-3 w-3 mr-1" />
                Reenviar código
              </>
            )}
          </button>
        </div>

        {/* Back Link */}
        <div className="mt-8 text-center">
          <Link
            href="/auth/signup"
            className="inline-flex items-center text-sm text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white"
          >
            <ArrowLeft className="h-4 w-4 mr-1" />
            Voltar ao cadastro
          </Link>
        </div>

        {/* Instructions */}
        <div className="mt-8 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
          <h3 className="text-sm font-medium text-gray-900 dark:text-white mb-2">
            Problemas para receber o email?
          </h3>
          <ul className="text-xs text-gray-600 dark:text-gray-400 space-y-1">
            <li>• Verifique sua caixa de spam</li>
            <li>• Certifique-se de que o email está correto</li>
            <li>• O código expira em 15 minutos</li>
            <li>• Tente reenviar o código se necessário</li>
          </ul>
        </div>
      </div>
    </div>
  );
}