/*!**********************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[3]!./src/styles/scrollbar.css ***!
  \**********************************************************************************************************************************************************************************************************************************************************************/
/* Estilos de scrollbar personalizados para diferentes módulos */

/* Estilo base para scrollbar */
::-webkit-scrollbar {
  width: 10px;
  height: 10px;
}

::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.05);
  border-radius: 10px;
}

::-webkit-scrollbar-thumb {
  border-radius: 10px;
  border: 2px solid transparent;
  background-clip: content-box;
  background-color: rgba(156, 163, 175, 0.5); /* Cor padrão - cinza */
}

::-webkit-scrollbar-thumb:hover {
  background-color: rgba(156, 163, 175, 0.7);
}

/* Dark mode base */
.dark::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.05);
}

.dark::-webkit-scrollbar-thumb {
  background-color: rgba(75, 85, 99, 0.5);
}

.dark::-webkit-scrollbar-thumb:hover {
  background-color: rgba(75, 85, 99, 0.7);
}

/* Aplicar ao body - com maior especificidade */
html body::-webkit-scrollbar {
  width: 12px;
  height: 12px;
}

html body::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.05);
  border-radius: 0;
}

html body::-webkit-scrollbar-thumb {
  border-radius: 10px;
  border: 3px solid transparent;
  background-clip: content-box;
  background-color: rgba(156, 163, 175, 0.5);
}

html body::-webkit-scrollbar-thumb:hover {
  background-color: rgba(156, 163, 175, 0.7);
}

/* Dark mode para body - com maior especificidade */
html.dark body::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.05);
}

html.dark body::-webkit-scrollbar-thumb {
  background-color: rgba(75, 85, 99, 0.5);
}

html.dark body::-webkit-scrollbar-thumb:hover {
  background-color: rgba(75, 85, 99, 0.7);
}

/* Módulo People (laranja) */
.module-people::-webkit-scrollbar-thumb {
  background-color: rgba(249, 115, 22, 0.4); /* orange-500 com opacidade */
}

.module-people::-webkit-scrollbar-thumb:hover {
  background-color: rgba(249, 115, 22, 0.6);
}

.dark.module-people::-webkit-scrollbar-thumb {
  background-color: rgba(249, 115, 22, 0.3);
}

.dark.module-people::-webkit-scrollbar-thumb:hover {
  background-color: rgba(249, 115, 22, 0.5);
}

/* Body - Módulo People - com maior especificidade */
html body.module-people::-webkit-scrollbar-thumb {
  background-color: rgba(249, 115, 22, 0.4) !important;
}

html body.module-people::-webkit-scrollbar-thumb:hover {
  background-color: rgba(249, 115, 22, 0.6) !important;
}

html.dark body.module-people::-webkit-scrollbar-thumb {
  background-color: rgba(249, 115, 22, 0.3) !important;
}

html.dark body.module-people::-webkit-scrollbar-thumb:hover {
  background-color: rgba(249, 115, 22, 0.5) !important;
}

/* Módulo Scheduler (roxo) */
.module-scheduler::-webkit-scrollbar-thumb {
  background-color: rgba(147, 51, 234, 0.4); /* violet-600 com opacidade */
}

.module-scheduler::-webkit-scrollbar-thumb:hover {
  background-color: rgba(147, 51, 234, 0.6);
}

.dark.module-scheduler::-webkit-scrollbar-thumb {
  background-color: rgba(147, 51, 234, 0.3);
}

.dark.module-scheduler::-webkit-scrollbar-thumb:hover {
  background-color: rgba(147, 51, 234, 0.5);
}

/* Body - Módulo Scheduler - com maior especificidade */
html body.module-scheduler::-webkit-scrollbar-thumb {
  background-color: rgba(147, 51, 234, 0.4) !important;
}

html body.module-scheduler::-webkit-scrollbar-thumb:hover {
  background-color: rgba(147, 51, 234, 0.6) !important;
}

html.dark body.module-scheduler::-webkit-scrollbar-thumb {
  background-color: rgba(147, 51, 234, 0.3) !important;
}

html.dark body.module-scheduler::-webkit-scrollbar-thumb:hover {
  background-color: rgba(147, 51, 234, 0.5) !important;
}

/* Módulo Admin (cinza/slate) */
.module-admin::-webkit-scrollbar-thumb {
  background-color: rgba(100, 116, 139, 0.4); /* slate-500 com opacidade */
}

.module-admin::-webkit-scrollbar-thumb:hover {
  background-color: rgba(100, 116, 139, 0.6);
}

.dark.module-admin::-webkit-scrollbar-thumb {
  background-color: rgba(100, 116, 139, 0.3);
}

.dark.module-admin::-webkit-scrollbar-thumb:hover {
  background-color: rgba(100, 116, 139, 0.5);
}

/* Body - Módulo Admin - com maior especificidade */
html body.module-admin::-webkit-scrollbar-thumb {
  background-color: rgba(100, 116, 139, 0.4) !important;
}

html body.module-admin::-webkit-scrollbar-thumb:hover {
  background-color: rgba(100, 116, 139, 0.6) !important;
}

html.dark body.module-admin::-webkit-scrollbar-thumb {
  background-color: rgba(100, 116, 139, 0.3) !important;
}

html.dark body.module-admin::-webkit-scrollbar-thumb:hover {
  background-color: rgba(100, 116, 139, 0.5) !important;
}

/* Módulo Financial (verde) */
.module-financial::-webkit-scrollbar-thumb {
  background-color: rgba(5, 150, 105, 0.4); /* emerald-600 com opacidade */
}

.module-financial::-webkit-scrollbar-thumb:hover {
  background-color: rgba(5, 150, 105, 0.6);
}

.dark.module-financial::-webkit-scrollbar-thumb {
  background-color: rgba(5, 150, 105, 0.3);
}

.dark.module-financial::-webkit-scrollbar-thumb:hover {
  background-color: rgba(5, 150, 105, 0.5);
}

/* Body - Módulo Financial - com maior especificidade */
html body.module-financial::-webkit-scrollbar-thumb {
  background-color: rgba(5, 150, 105, 0.4) !important;
}

html body.module-financial::-webkit-scrollbar-thumb:hover {
  background-color: rgba(5, 150, 105, 0.6) !important;
}

html.dark body.module-financial::-webkit-scrollbar-thumb {
  background-color: rgba(5, 150, 105, 0.3) !important;
}

html.dark body.module-financial::-webkit-scrollbar-thumb:hover {
  background-color: rgba(5, 150, 105, 0.5) !important;
}

/* Estilo para modais */
.modal-scrollbar::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.modal-scrollbar::-webkit-scrollbar-thumb {
  border-width: 1px;
}

/* Aplicar scrollbar personalizado a elementos específicos */
.custom-scrollbar {
  scrollbar-width: thin; /* Para Firefox */
  scrollbar-color: rgba(156, 163, 175, 0.5) rgba(0, 0, 0, 0.05); /* Para Firefox */
}

/* Aplicar scrollbar personalizado a elementos específicos por módulo */
.custom-scrollbar.module-people {
  scrollbar-color: rgba(249, 115, 22, 0.4) rgba(0, 0, 0, 0.05); /* Para Firefox */
}

.custom-scrollbar.module-scheduler {
  scrollbar-color: rgba(147, 51, 234, 0.4) rgba(0, 0, 0, 0.05); /* Para Firefox */
}

.custom-scrollbar.module-admin {
  scrollbar-color: rgba(100, 116, 139, 0.4) rgba(0, 0, 0, 0.05); /* Para Firefox */
}

.custom-scrollbar.module-financial {
  scrollbar-color: rgba(5, 150, 105, 0.4) rgba(0, 0, 0, 0.05); /* Para Firefox */
}

/* Dark mode para Firefox */
.dark .custom-scrollbar {
  scrollbar-color: rgba(75, 85, 99, 0.5) rgba(255, 255, 255, 0.05);
}

.dark .custom-scrollbar.module-people {
  scrollbar-color: rgba(249, 115, 22, 0.3) rgba(255, 255, 255, 0.05);
}

.dark .custom-scrollbar.module-scheduler {
  scrollbar-color: rgba(147, 51, 234, 0.3) rgba(255, 255, 255, 0.05);
}

.dark .custom-scrollbar.module-admin {
  scrollbar-color: rgba(100, 116, 139, 0.3) rgba(255, 255, 255, 0.05);
}

.dark .custom-scrollbar.module-financial {
  scrollbar-color: rgba(5, 150, 105, 0.3) rgba(255, 255, 255, 0.05);
}

