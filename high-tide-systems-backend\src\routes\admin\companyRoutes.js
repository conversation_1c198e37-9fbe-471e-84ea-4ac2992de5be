// src/routes/companyRoutes.js
const express = require('express');
const router = express.Router();
const { authenticate } = require('../../middlewares/auth');
const systemAdminMiddleware = require('../../middlewares/systemAdmin');
const upload = require('../../middlewares/upload');
const { CompanyController, companyValidation } = require('../../controllers/companyController');
const AdvancedCacheMiddleware = require('../../middlewares/advancedCache');

// Todas as rotas requerem autenticação
router.use(authenticate);

// IMPORTANTE: Rotas específicas precisam vir ANTES das rotas com parâmetros (:id)
// para evitar que o Express interprete 'select' como um ID

router.get('/current',
  AdvancedCacheMiddleware.sessionCache('companies:current', 900), // 15 minutos
  CompanyController.getCurrentCompany
);

// Rota para sincronizar módulos de usuários com assinatura da empresa
router.post('/:id/sync-modules',
  systemAdminMiddleware,
  AdvancedCacheMiddleware.smartInvalidation({
    patterns: ['users:*', 'companies:*'],
    tags: ['users', 'companies']
  }),
  CompanyController.syncCompanyModules
);

// Rota para listar empresas para selecionar no formulário
router.get('/select',
  systemAdminMiddleware,
  AdvancedCacheMiddleware.referenceCache('companies:select', 1800), // 30 minutos
  CompanyController.listForSelect
);

// Rota para listar todas as empresas (com paginação)
router.get('/',
  AdvancedCacheMiddleware.smartCache({
    prefix: 'companies:list',
    ttl: 600, // 10 minutos
    strategy: 'search',
    tags: ['companies']
  }),
  CompanyController.list
);

// Rotas que exigem permissão de SYSTEM_ADMIN
// Criação de nova empresa
router.post('/',
  systemAdminMiddleware,
  upload.single('logo'),
  companyValidation,
  AdvancedCacheMiddleware.smartInvalidation({
    patterns: ['companies:*'],
    tags: ['companies', 'reference']
  }),
  CompanyController.create
);

// Alternar status de empresa (ativar/desativar)
router.patch('/:id/status',
  systemAdminMiddleware,
  AdvancedCacheMiddleware.smartInvalidation({
    patterns: ['companies:*'],
    tags: ['companies', 'reference']
  }),
  CompanyController.toggleStatus
);

// Excluir empresa
router.delete('/:id',
  systemAdminMiddleware,
  AdvancedCacheMiddleware.smartInvalidation({
    patterns: ['companies:*'],
    tags: ['companies', 'reference']
  }),
  CompanyController.delete
);

// Rotas com parâmetros que não exigem permissão de SYSTEM_ADMIN
// (a segurança é implementada no controller que verifica o papel do usuário)
router.get('/:id',
  AdvancedCacheMiddleware.smartCache({
    prefix: 'companies:detail',
    ttl: 900, // 15 minutos
    strategy: 'standard',
    tags: ['companies']
  }),
  CompanyController.get
);

router.put('/:id',
  upload.single('logo'),
  companyValidation,
  AdvancedCacheMiddleware.smartInvalidation({
    patterns: ['companies:*'],
    tags: ['companies', 'reference']
  }),
  CompanyController.update
);

module.exports = router;